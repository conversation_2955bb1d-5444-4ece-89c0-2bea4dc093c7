"use client"
import React, { useState } from "react";
import Image from "next/image";
import Tabs from "../tab";
import Heading from "../Heading/Heading";

const HomeP5 = () => {
  const [activeTab, setActiveTab] = useState("Web");

  // Define technology data for each tab
  const technologyData = {
    Web: [
      { src: "/Images/css.png", alt: "css", width: 29, height: 41, className: "w-[29px] h-[41px] md:w-[57px] md:h-[80px] object-cover" },
      { src: "/Images/html.png", alt: "html", width: 29, height: 41, className: "w-[29px] h-[41px] md:w-[57px] md:h-[80px] object-contain" },
      { src: "/Images/javascript.png", alt: "javascript", width: 36, height: 36, className: "w-[36px] h-[36px] md:w-[80px] md:h-[80px] object-cover" },
      { src: "/Images/python.png", alt: "python", width: 36, height: 36, className: "w-[36px] h-[36px] md:w-[74px] md:h-[75px] object-cover" },
      { src: "/Images/php-logo.png", alt: "php", width: 50, height: 24, className: "w-[50px] h-[24px] md:w-[98px] md:h-[47px] object-contain", hiddenOnMobile: true },
      { src: "/Images/react.png", alt: "react", width: 33, height: 41, className: "w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-cover" },
      { src: "/Images/node.png", alt: "node", width: 33, height: 41, className: "w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-contain" },
      { src: "/Images/java-logo.png", alt: "java", width: 57, height: 30, className: "w-[57px] h-[30px] md:w-[90px] md:h-[56px] object-contain" },
      { src: "/Images/Dotnet.png", alt: "Dotnet", width: 35, height: 34, className: "w-[35px] h-[34px] md:w-[75px] md:h-[72px] object-cover" },
    ],
    Mobile: [
      { src: "/Images/Mobile1.png", alt: "Mobile Technology 1", width: 36, height: 36, className: "w-[36px] h-[36px] md:w-[80px] md:h-[80px] object-cover" },
      { src: "/Images/Mobile2.png", alt: "Mobile Technology 2", width: 33, height: 41, className: "w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-cover" },
      { src: "/Images/Mobile3.png", alt: "Mobile Technology 3", width: 29, height: 41, className: "w-[29px] h-[41px] md:w-[57px] md:h-[80px] object-contain" },
      { src: "/Images/Mobile4.png", alt: "Mobile Technology 4", width: 36, height: 36, className: "w-[36px] h-[36px] md:w-[74px] md:h-[75px] object-cover" },
      { src: "/Images/Mobile5.png", alt: "Mobile Technology 5", width: 50, height: 24, className: "w-[50px] h-[24px] md:w-[98px] md:h-[47px] object-contain" },
      { src: "/Images/Mobile6.png", alt: "Mobile Technology 6", width: 57, height: 30, className: "w-[57px] h-[30px] md:w-[90px] md:h-[56px] object-contain" },
      { src: "/Images/Mobile7.png", alt: "Mobile Technology 7", width: 35, height: 34, className: "w-[35px] h-[34px] md:w-[75px] md:h-[72px] object-cover" },
    ],
    "UI/UX Design": [
      { src: "/Images/UIUX1.png", alt: "UI/UX Design Tool 1", width: 36, height: 36, className: "w-[36px] h-[36px] md:w-[80px] md:h-[80px] object-cover" },
      { src: "/Images/UIUX2.png", alt: "UI/UX Design Tool 2", width: 33, height: 41, className: "w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-cover" },
    ]
  };

  const currentTechnologies = technologyData[activeTab] || technologyData.Web;

  return (
    <div>
      <section className="w-[90%] md:w-[85%] mx-auto">
        <Heading>
          <span className="text-[#7716BC]">languages</span> and programming{" "}
          <span className="text-[#7716BC]">codes</span>
        </Heading>

        <Tabs activeTab={activeTab} setActiveTab={setActiveTab} />
        <div className="w-[90%] md:w-[80%] mx-auto mt-[42px]">
          {/* Dynamic Grid container */}
          <div className="grid gap-[42px]">
            {/* First row - up to 5 items on desktop, 4 on mobile */}
            <div className="grid grid-cols-4 md:grid-cols-5 gap-[42px]">
              {currentTechnologies.slice(0, 4).map((tech, index) => (
                <div key={index} className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                  <Image
                    src={tech.src}
                    alt={tech.alt}
                    width={tech.width}
                    height={tech.height}
                    className={tech.className}
                  />
                </div>
              ))}
              {/* 5th item only on desktop */}
              {currentTechnologies[4] && (
                <div className="hidden md:flex w-[72px] md:w-[134px] h-[72px] md:h-[134px] items-center justify-center p-4 md:p-5 bg-white rounded-md">
                  <Image
                    src={currentTechnologies[4].src}
                    alt={currentTechnologies[4].alt}
                    width={currentTechnologies[4].width}
                    height={currentTechnologies[4].height}
                    className={currentTechnologies[4].className}
                  />
                </div>
              )}
            </div>

            {/* Second row - remaining items centered */}
            {currentTechnologies.length > 5 && (
              <div className="flex justify-center items-center gap-[16px] md:gap-[42px]">
                {currentTechnologies.slice(5, 9).map((tech, index) => (
                  <div key={index + 5} className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                    <Image
                      src={tech.src}
                      alt={tech.alt}
                      width={tech.width}
                      height={tech.height}
                      className={tech.className}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* Third row for mobile - 5th item from first row */}
            {currentTechnologies[4] && (
              <div className="flex justify-center md:hidden">
                <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                  <Image
                    src={currentTechnologies[4].src}
                    alt={currentTechnologies[4].alt}
                    width={currentTechnologies[4].width}
                    height={currentTechnologies[4].height}
                    className={currentTechnologies[4].className}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
      <section></section>
    </div>
  );
};
export default HomeP5;
