import React from "react";
import Image from "next/image";
import Tabs from "../tab";
import Heading from "../Heading/Heading";

const HomeP5 = () => {
  
  return (
    <div>
      <section className="w-[90%] md:w-[85%] mx-auto">
        <Heading>
          <span className="text-[#7716BC]">languages</span> and programming{" "}
          <span className="text-[#7716BC]">codes</span>
        </Heading>
       
        <Tabs />
        <div className="w-[90%] md:w-[80%] mx-auto mt-[42px]">
          {/* Grid container */}
          <div className="grid gap-[42px]">
            {/* Row 1 - 5 containers on larger screens, 4 on smaller screens */}
            <div className="grid grid-cols-4 md:grid-cols-5 gap-[42px]">
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/css.png"
                  alt="css"
                  width={29}
                  height={41}
                  className="w-[29px] h-[41px] md:w-[57px] md:h-[80px] object-cover"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/html.png"
                  alt="html"
                  width={29}
                  height={41}
                  className="w-[29px] h-[41px] md:w-[57px] md:h-[80px] object-contain"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/javascript.png"
                  alt="javascript"
                  width={36}
                  height={36}
                  className="w-[36px] h-[36px] md:w-[80px] md:h-[80px] object-cover"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/python.png"
                  alt="python"
                  width={36}
                  height={36}
                  className="w-[36px] h-[36px] md:w-[74px] md:h-[75px] object-cover"
                />
              </div>
              <div className="hidden md:flex w-[72px] md:w-[134px] h-[72px] md:h-[134px] items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/php-logo.png"
                  alt="php"
                  width={50}
                  height={24}
                  className="w-[50px] h-[24px] md:w-[98px] md:h-[47px] object-contain"
                />
              </div>
            </div>

            {/* Row 2 - 4 containers centered on larger screens, 4 containers on smaller screens */}
            <div className="flex justify-center items-center gap-[16px] md:gap-[42px]">
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/react.png"
                  alt="react"
                  width={33}
                  height={41}
                  className="w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-cover"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/node.png"
                  alt="node"
                  width={33}
                  height={41}
                  className="w-[33px] h-[41px] md:w-[71px] md:h-[89px] object-contain"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/java-logo.png"
                  alt="java"
                  width={57}
                  height={30}
                  className="w-[57px] h-[30px] md:w-[90px] md:h-[56px] object-contain"
                />
              </div>
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/Dotnet.png"
                  alt="Dotnet"
                  width={35}
                  height={34}
                  className="w-[35px] h-[34px] md:w-[75px] md:h-[72px] object-cover"
                />
              </div>
            </div>

            {/* Row 3 (only for smaller screens) - Single container centered */}
            <div className="flex justify-center md:hidden">
              <div className="w-[72px] md:w-[134px] h-[72px] md:h-[134px] flex items-center justify-center p-4 md:p-5 bg-white rounded-md">
                <Image
                  src="/Images/php-logo.png"
                  alt="php"
                  width={50}
                  height={24}
                  className="w-[50px] h-[24px] md:w-[98px] md:h-[47px] object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <section></section>
    </div>
  );
};
export default HomeP5;
