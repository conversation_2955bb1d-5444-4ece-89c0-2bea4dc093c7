import React from 'react'
import <PERSON><PERSON> from '../Buttons/Button'

const Section2 = () => {
  return (
    <section className="w-full py-8 md:py-16">
      <div className="w-[90%] mx-auto">
        <div className="flex flex-col md:flex-row gap-8 md:gap-0">
          {/* Form Section - 50% width */}
          <div className="w-full md:w-[50%] px-4 md:px-8">
            <div className="mb-6">
              <h2 className="text-2xl md:text-3xl font-bold text-[#7716BC] mb-4">
                Get In Touch
              </h2>
              <p className="text-gray-600 text-base md:text-lg">
                Ready to start your project? Contact us today and let's discuss how we can help bring your vision to life.
              </p>
            </div>

            <form className="flex flex-col gap-4 bg-[#EE75CD1A] p-4 md:p-8 rounded-md">
              <input
                type="text"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="name"
                id="name"
                placeholder="Name"
                required
              />
              <input
                type="email"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="email"
                id="email"
                placeholder="Email"
                required
              />
              <input
                type="tel"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1]"
                name="phone"
                id="phone"
                placeholder="Phone Number"
                required
              />
              <textarea
                name="message"
                id="message"
                className="p-3 rounded-sm border border-gray-300 focus:outline-none focus:border-[#F245A1] mb-4"
                rows={4}
                placeholder="Message"
                required
              ></textarea>
              <Button bgColor="bg-[#F245A1]" hoverColor="opacity-90">
                Send Message
              </Button>
            </form>
          </div>

          {/* Map Section - 50% width */}
          <div className="w-full md:w-[50%] px-4 md:px-8">
            <div className="mb-6">
              <h3 className="text-xl md:text-2xl font-bold text-[#7716BC] mb-4">
                Our Location
              </h3>
              <div className="text-gray-600 mb-4">
                <p className="font-semibold">Address:</p>
                <p>16192 Coastal Highway</p>
                <p>Lewes, Delaware 19958</p>
                <p>USA</p>
              </div>
            </div>

            {/* Google Maps Embed */}
            <div className="w-full h-[300px] md:h-[400px] rounded-md overflow-hidden shadow-lg">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3048.2!2d-75.1397!3d38.7223!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89b8c6c6c6c6c6c6%3A0x1234567890abcdef!2s16192%20Coastal%20Hwy%2C%20Lewes%2C%20DE%2019958%2C%20USA!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Our Location - 16192 Coastal Highway, Lewes, Delaware 19958, USA"
              ></iframe>
            </div>

            {/* Contact Information */}
            <div className="mt-6 p-4 bg-[#EE75CD1A] rounded-md">
              <h4 className="font-semibold text-[#7716BC] mb-2">Contact Information</h4>
              <div className="space-y-2 text-gray-600">
                <p><span className="font-medium">Phone:</span> +****************</p>
                <p><span className="font-medium">Email:</span> <EMAIL></p>
                <p><span className="font-medium">Working Hours:</span> Monday To Friday 9:00 AM to 5:00 PM EST</p>
              </div>
            </div>
          </div>
        </div>

        {/* Three Cards Section */}
        <div className="mt-16 md:mt-24">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            {/* Address Card */}
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Address</h3>
              </div>
              <div className="text-gray-600">
                <p>16192 Coastal Highway Lewes,</p>
                <p>Delaware 19958, USA</p>
              </div>
            </div>

            {/* Working Hours Card */}
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Working Hours</h3>
              </div>
              <div className="text-gray-600">
                <p>Monday To Friday</p>
                <p>9:00 AM to 5:00 PM EST</p>
                <p className="mt-2 text-sm">Our Support Team is available</p>
              </div>
            </div>

            {/* Contact Us Card */}
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-[#7716BC] rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Contact Us</h3>
              </div>
              <div className="text-gray-600">
                <p>+****************</p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Section2