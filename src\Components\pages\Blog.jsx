"use client";
import React, { useState } from "react";
import BlogCard from "../Blog/BlogCard";
import Button from "../Buttons/Button";
import Heading from "../Heading/Heading";
import Link from "next/link";

const Blog = ({ title, heading, description }) => {
  const [showAll, setShowAll] = useState(false); // State for "See More/Less"
  const [currentSlide, setCurrentSlide] = useState(0); // Track active slide for mobile

  const cardData = [
    {
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "27 Jan 2021",
      imageUrl: "/Images/laptops-use.png",
    },
    {
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "5 Feb 2024",
      imageUrl: "/Images/laptops-use.png",
    },
    {
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "12 Mar 2024",
      imageUrl: "/Images/laptops-use.png",
    },
    {
      title: "10 Inspiring Reasons to Start an Online Business in 2024",
      description: "Read about the reasons to start an online business",
      date: "12 Mar 2024",
      imageUrl: "/Images/laptops-use.png",
    },
  ];

  // Handle carousel scroll
  const handleScroll = (e) => {
    const slideIndex = Math.round(e.target.scrollLeft / e.target.clientWidth);
    setCurrentSlide(slideIndex);
  };

  // Cards to display based on state
  const visibleCards = showAll ? cardData : cardData.slice(0, 3);

  return (
    <div className="my-24">
      {/* Heading Section */}
      <div className="text-center">
        <h2 className="text-[#F245A1] text-lg md:text-2xl font-medium mb-2">
          {heading}
        </h2>
        <Heading>{title}</Heading>
        <h2 className="text-xl md:text-2xl text-[#232536] font-semibold leading-9 md:leading-[57px]"></h2>
        <p className="text-xl text-[#232222]">{description}</p>
      </div>

      {/* Carousel for Mobile Screens */}
      <div
        className="flex gap-5 overflow-x-scroll md:hidden snap-x snap-mandatory scrollbar-hide"
        onScroll={handleScroll}
      >
        {cardData.map((card, index) => (
          <div
            key={index}
            className={`flex-shrink-0 w-[85%] px-4 mx-auto ${
              index === 0 ? "ml-[7.5%]" : "" // Adjust margin for the first card
            }`}
          >
            <BlogCard
              title={card.title}
              description={card.description}
              date={card.date}
              imageUrl={card.imageUrl}
            />
          </div>
        ))}
      </div>

      {/* Carousel Dots for Mobile */}
      <div className="flex justify-center mt-4 md:hidden">
        {cardData.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 mx-1 rounded-full ${
              currentSlide === index ? "bg-[#7716BC]" : "bg-gray-300"
            }`}
          ></div>
        ))}
      </div>

      {/* Grid for Larger Screens */}
      <div className="hidden md:flex justify-evenly gap-4 items-center my-10 ">
        {visibleCards.map((card, index) => (
          <BlogCard
            key={index}
            title={card.title}
            description={card.description}
            date={card.date}
            imageUrl={card.imageUrl}
          />
        ))}
      </div>

      {/* "See More/Less" Button */}
      <div className="w-fit mx-auto hidden md:block">
        <Button to="/blog" paddingX="px-6" paddingY="py-1" bgColor="bg-[#F245A1]" hoverColor="opacity-70">View All</Button>
      </div>
    </div>
  );
};

export default Blog;
