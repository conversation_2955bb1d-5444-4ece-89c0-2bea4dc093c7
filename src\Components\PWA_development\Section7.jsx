import ImageCard from "./ImageCard";



const Section7 = ({ImageCardData,heading, paragrapgh,spanHeading}) => {
  return (
    <div className="mx-[20px] mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold mb-1">
        <span className="text-[#7716BC]">{spanHeading}</span> {heading} 
      </h2>
      <p className="md:w-[85%] md:mx-auto mb-4 text-base md:text-xl text-center">
        {paragrapgh}  
      </p>
      <div className="grid grid-cols-1  md:grid-cols-2 gap-8">
        {ImageCardData.map((card, index) => {
          // Check if it's the last card
          const isLastCard = index === ImageCardData.length - 1;
          return (
            <div
              key={index}
              className={`${isLastCard ? "md:col-span-2 flex justify-center" : "mx-auto"}`}
            >
              <ImageCard
                imgsrc={card.imgsrc}
                altsrc={card.altsrc}
                title={card.title}
                description={card.description}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Section7;
