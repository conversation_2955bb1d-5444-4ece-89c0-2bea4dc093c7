import React from "react";
import Card from "./Card_2";

const Section6 = () => {
  const cardData = [
    {
      title: "Software Industry",
      content: [
        "Data Synchronization",
        "3rd Party Software Integration",
        "Cloud App Integration",
        "API Development",
      ],
    },
    {
      title: "Healthcare Industry",
      content: [
        "Telemedicine Web Integration",
        "EMR Integration",
        "Integration of Laboratory Systems",
        "Patient Portals",
      ],
    },
    {
      title: "Legal Industry",
      content: [
        "Compliance Management",
        "ERP Integration",
        "Data Migration",
        "Workflows Automation",
      ],
    },
    {
      title: "Retail Industry",
      content: [
        "POS Systems",
        "Supply Chain Integration",
        "Inventory Integration",
        "E-commerce Integration",
      ],
    },
    {
      title: "Real Estate",
      content: [
        "Market Data Integration",
        "Real estate IT services",
        "Investment Tools Integration",
        "Tenant Portal Integration",
        "Integration of Property Management Software",
      ],
    },
    {
      title: "Manufacturing Industry",
      content: [
        "Logistics Integration",
        "MES Integration",
        "Quality Control Monitoring",
        "Sensor Integration through IOT deployment services",
        "Robotics Systems",
      ],
    },
  ];

  return (
    <div className="w-[85%] mx-auto">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold capitalize">
        We’ve Delivered Seamless{" "}
        <span className="text-[#F245A1]">App to App Integration Services</span>{" "}
        for Diverse Industries
      </h2>
      <div className="container mx-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cardData.map((card, index) => (
            <Card
              key={index}
              title={card.title}
              content={card.content}
              className="h-full"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Section6;
