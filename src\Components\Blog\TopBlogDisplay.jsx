import Image from "next/image";
import React from "react";
import Link from "next/link";

const blogs = [
  {
    title: "Flutter App Development Best Practices to Follow in 2023",
    image: "/Images/using_laptops.png",
  },
  {
    title: "Flutter App Development Best Practices to Follow in 2023",
    image: "/Images/using_laptops.png",
  },
  {
    title: "Flutter App Development Best Practices to Follow in 2023",
    image: "/Images/using_laptops.png",
  },
];

const TopBlogDisplay = () => {
  return (
    <>
      <div className="max-w-[85vw] mx-auto mt-6 md:mt-12 relative flex flex-col md:flex-row justify-center items-center gap-6 md:gap-10">
        <div className="flex">
          <div
            className="block w-[286px] md:w-[400px] h-[278px] md:h-[300px] px-3 md:px-4 py-3 md:py-6 bg-white border-gray-200 rounded-lg shadow hover:bg-gray-100 absolute z-10 left-[-4px] md:left-[-44px] top-[32px] md:top-[40px] overflow-y-hidden"
            style={{ maxHeight: "300px" }} // Fixed height
          >
            <h5 className="mb-2 text-xl md:text-2xl font-bold tracking-tight text-black">
              Flutter App Development Best Practices to Follow in 2023 
            </h5>
            <p className="font-normal text-sm md:text-lg text-black">
              The dream of being your boss is a universal aspiration. An online
              business offers unprecedented freedom and control. You have the
              liberty to set your own hours, choose your projects, and build a
              business that aligns with your passions and ...
            </p>
          </div>

          <div className="w-[339px] md:w-[648px] h-[342px] md:h-[440px] relative">
            <Image
              src={"/Images/using_laptops.png"}
              alt="Selected Blog Image"
              layout="fill"
              objectFit="cover"
           
            />
          </div>
        </div>

        <div className="flex-col gap-12">
          {blogs.map((blog, index) => (
            <Link href="/blogpage" passHref key={index}>
              <div
                className="flex gap-8 mt-5 cursor-pointer"
                // onClick={() => setSelectedBlog(blog)}
                // Update state on click
              >
                <div className="w-[92px] md:w-[115px] h-[86px] md:h-[108px] relative">
                  <Image
                    src={blog.image}
                    alt="Blog Thumbnail"
                    layout="fill"
                    objectFit="cover"
                  />
                </div>

                <div className="flex-1">
                  <h4 className="text-lg md:text-xl font-medium leading-{27px} md:leading-[36px]">
                    {blog.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
};

export default TopBlogDisplay;
