"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";

const Industries = () => {
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/industries-nav.jpeg"} width={400} height={400} />
        </div>
        
        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Industries/Healthcare" className=" text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Healthcare
            </Link>
            <Link href="/Industries/Gaming" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Gaming
            </Link>
            <Link href="/Industries/E-Commerce" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              E-commerce
            </Link>
            <Link href="/Industries/Agriculture" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Agriculture
            </Link>
            <Link href="/Industries/Travel" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Travel
            </Link>
            <Link href="/Industries/Automotive" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Automotive
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Industries/Finance" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Finance
            </Link>
            <Link href="/Industries/Education" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Education
            </Link>
            <Link href="/Industries/Social_Networking" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Social Networking
            </Link>
            <Link href="/Industries/Oil_And_Gas" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Oil and Gas
            </Link>
            <Link href="/Industries/Real_Estate" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Real Estate
            </Link>
            <Link href="/Industries/Insurance" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Insurance
            </Link>
          </div>

          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Industries/Telecom" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Telecom
            </Link>
            <Link href="/Industries/Construction" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Construction
            </Link>
            <Link href="/Industries/Manufacturing" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Manufacturing
            </Link>
            <Link href="/Industries/Logistics" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Logistics
            </Link>
            <Link href="/Industries/Banking" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Banking
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Industries;
