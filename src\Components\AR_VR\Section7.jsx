import Image from "next/image";



const Section8 = ({ cardData, image,Heading, spanHeading, paragrapgh }) => {
  return (
    <div className="bg-blue-100 px-4 mb-10 md:mb-24 md:py-10 md:px-[75px] py-5">
      <h2 className="text-xl md:text-[28px] font-semibold text-center">
       {Heading}{" "}
        <span className="text-[#F245A1]">{spanHeading}</span>
      </h2>
      <p className="text-xl md:w-[70%] mx-auto text-center">
        {paragrapgh}
      </p>

      <div className="flex flex-col-reverse md:flex-row justify-center items-stretch gap-5 mt-6 md:mt-[42px]">
        <div className="w-full md:w-[60%]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {cardData.map((card, index) => (
              <InfoCard
                key={index}
                title={card.title}
                description={card.description}
              />
            ))}
          </div>
        </div>
        <div className="relative w-full md:w-[30%] min-h-[250px] md:min-h-0">
          {/* Use fill + object-cover to cover the entire container */}
          <Image
            src={image}
            alt="AI"
            fill
            className="object-cover"
          />
        </div>
      </div>
    </div>
  );
};


export default Section8;
const InfoCard = ({ title, description }) => {
  return (
    <div className="w-full md:max-w-md flex items-start gap-1 border border-pink-500 p-2  rounded-lg shadow-sm">
      <div className="w-[20px] h-[20px] shrink-0">
        <Image
          src="/Images/service_frame.png"
          alt="arrow"
          width={50}
          height={50}
          className="object-contain"
        />
      </div>
      <div>
        <h3 className="font-semibold  text-base">{title}:</h3>
        <p className="text-sm text-justify">{description}</p>
      </div>
    </div>
  );
};