import React from "react";
import PinkDotCard from "./PinkDotCard";

const Section2 = ({
  cardContent,
  leftHeading,
  leftParagrapgh,
  spanHeading,
  rightHeading,
  blueHeading,
  blueCardClasses,
  bluecardText
  
}) => {
  return (
    <section className="bg-white mb-5 p-4 md:p-8">
      <div className="md:my-[32px] md:mx-[75px] flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
        <div className="flex-1">
          <h2 className="text-xl md:text-[28px] md:leading-7 font-semibold mb-1">
            {leftHeading}
            <span className="text-[#F245A1]">{spanHeading}</span> 
            <span className="text-[#7716BC]">{blueHeading}</span> {rightHeading}
          </h2>
          <p className="text-sm md:text-lg capitalize text-justify">
            {leftParagrapgh}
          </p>
        </div>
        <div className="flex-1">
          <PinkDotCard cardContent={cardContent} />
          <div className={`${blueCardClasses}`}>
          <p className="text-center">{bluecardText}</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section2;
