import React from "react";
import Card from "./Card";

const Section5 = () => {
  const cardData = [
    {
      title: "Front End Development",
      description:
        "The front end of applications that encourage interaction is an incredibly important part of a website and that is the reason we have a keen focus on it. They are meticulously designed by our team of full-stack developers. The expert developers are proficient in all the most popular front-end development languages, including the latest iterations of Vue.js, AngularJS, Ext.js, Mocha, and React.",
      bgColor: "bg-blue-100",
    },
    {
      title: "Backend Development",
      description:
        "Our developers are always working with the latest technologies such as .NET, Java, Python, and more which save you time and resources. Our talented team ensures they produce results that meet your expectations. Working with our back-end developers guarantees that you will get the most lucrative results.",
      bgColor: "bg-gray-100",
    },
    {
      title: "Database Design",
      description:
        "We use the latest technologies to create reliable databases that manage, store, and retrieve data for the best possible application performance. Database design is the main area of expertise for our committed team of in-house professionals. Because our experts are skilled in dealing with a variety of databases, such as NoSQL, MongoDB, IBM Db2, PostgreSQL, and MySQL.",
      bgColor: "bg-gray-100",
    },
    {
      title: "API Integration",
      description:
        "Our resolute team of developers integrates several services, including email marketing, social networks, business tools, payment systems, and geo-services instead of integrating with non-customized third-party APIs. We strive to offer customized solutions to address your business challenges and drive growth for your business.",
      bgColor: "bg-blue-100",
    },
    {
      title: "Mobile App Development",
      description:
        "Using an advanced tech stack, our team of full-stack developers builds high-performing, user-friendly mobile apps that drive excellent user experience and better engagement. Our team is experienced in designing apps across both Android and iOS applications so you can cater to both kinds of customers.",
      bgColor: "bg-blue-100",
    },
    {
      title: "Full Stack Consultancy Services",
      description:
        "Our Full Stack Consultancy Services at Valueans provide thorough direction and knowledge to assist you in navigating the challenges of full-stack development. To provide strategic guidance on the appropriate technology and other critical aspects of development, our experienced consultants are always here to help you.",
      bgColor: "bg-gray-100",
    },
    {
      title: "Full Stack Migration and Porting",
      description:
        "Our full stack developers at Valueans have the expertise to seamlessly port and migrate your existing applications to your chosen full stack technologies. We guarantee little downtime, no data loss, minimal disturbance, and high performance when transferring or porting your apps. Our experienced team strives hard to make the integration as smooth as possible.",
      bgColor: "bg-gray-100",
    },
    {
      title: "Full Stack Development with AI Integration",
      description:
        "At Valueans we help you extend your web app’s functionalities by integrating it with AI. Our team of talented developers seamlessly integrates advanced AI/ML models. The AI integration helps in taking the most advantage of NLP, enabling image recognition, and performing predictive analysis. Our full stack custom website development services help transform your current business operations into intelligent business operations.",
      bgColor: "bg-blue-100",
    },
  ];

  return (
    <section className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        Partner with a Full Stack Development <br /> Agency for better{" "}
        <span className="text-[#F245A1]">ROI</span>
      </h2>
      <div className="w-full md:max-w-[75%] mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {cardData.map((card, index) => (
            <Card
              key={index}
              title={card.title}
              description={card.description}
              bgColor={card.bgColor}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Section5;
