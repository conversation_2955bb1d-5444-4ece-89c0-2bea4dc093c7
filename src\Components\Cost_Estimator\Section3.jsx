import React from "react";
import PinkTopCard from "../PWA_development/PinkTopCard";
import BlueTopCard from "../Industries/E-commerce/BlueTopCard";

const Section3 = ({
  heading,
  paragraph,
  card1Title,
  card1Description1,
  card1Description2,
  card2Title,
  card2Description1,
  card2Description2,
  card3Title,
  card3Description1,
  card3Description2,
  card4Title,
  card4Description1,
  card4Description2,
}) => {
  return (
    <div className="w-[90%] mx-auto mb-10 md:my-24 mt-10">
      {/* Changed from flex to flex-col for mobile and row for md+ */}
      <div className="flex flex-col md:flex-row">
        {/* Text section - full width on mobile, 50% on larger screens */}
        <div className="w-full md:w-[50%] mb-8 md:mb-0">
          <h1 className="text-xl md:text-3xl font-semibold mb-4">{heading}</h1>
          <p className="text-base">{paragraph}</p>
        </div>

        {/* Cards section - full width on mobile, 50% on larger screens */}
        <div className="w-full md:w-[50%] flex flex-col space-y-6 md:space-y-10 max-h-full md:h-[500px] md:overflow-y-auto">
          {/* Card 1 - full width on mobile */}
          <div className="w-full">
            <PinkTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card1Title}
              description={card1Description1}
              description2={card1Description2}
            />
          </div>

          {/* Card 2 - full width on mobile, aligned right on larger screens */}
          <div className="w-full md:flex md:justify-end">
            <BlueTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card2Title}
              description={card2Description1}
              description2={card2Description2}
            />
          </div>

          {/* Card 3 - full width on mobile */}
          <div className="w-full">
            <PinkTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card3Title}
              description={card3Description1}
              description2={card3Description2}
            />
          </div>

          {/* Card 4 - full width on mobile, aligned right on larger screens */}
          <div className="w-full md:flex md:justify-end">
            <BlueTopCard
              cardHeaderHeight={"h-[50px]"}
              title={card4Title}
              description={card4Description1}
              description2={card4Description2}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Section3;
