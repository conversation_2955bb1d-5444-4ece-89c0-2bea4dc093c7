import React from "react";
import Image from "next/image";

const HomeP2 = () => {
  return (
    <section className="w-[90%]  mx-auto">
      <div className="text-center font-poppins rounded-t-3xl bg-[#F6EBF6]  py-2 md:py-8 px-4 md:px-[118px] mt-4 md:mt-8 mb-10 md:mb-24">
        <h2 className="text-xl font-bold md:text-2xl text-[#232536]">
          <span className="text-3xl md:text-[42px] text-[#8B4AD1] mr-2">80%</span>
          of the solution that you need is already waiting for you
        </h2>
        <p className="text-base md:text-lg  text-[#232222]   mt-1 md:mt-3">
          Let’s not waste time developing from scratch because 80% of the code
          you need already exists. So why not benefit from something that has
          already been tested and validated? Saves you precious time, money, and
          energy!
        </p>
      </div>
      <section>
        <div className="  container  px-4 py-8 font-sans">
          <div className="flex flex-col md:flex-row justify-between items-center gap-8 mb-12">
            <div className="flex-1">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-bold mb-3 md:mb-6 ">
                Introducing ReOps by Valueans
              </h2>
              <p className="mb-1 md:mb-4 text-justify text-[#232222] text-sm md:text-lg">
                At Valueans, we understand the urgency and complexity of
                software development projects which is why we present to you-
                ReOps. An invention that simplifies and accelerates the code
                reuse process, making innovation more accessible and impactful
                from the get-go.
              </p>
              <p className="mb-1 md:mb-4 text-justify text-[#232222] text-sm md:text-lg">
                Where you simply provide us with your requirements, and we
                spring into action, utilizing our extensive library of
                pre-existing code modules. This means no more starting from
                scratch; instead, we leverage our arsenal of proven, reusable
                code components, enabling rapid and cost-effective development
                without compromising quality.
              </p>
            </div>
            <div className="flex flex-1 justify-end items-center relative bottom-[66px] mt-8 md:mt-0">
              <Image
                src="/Images/bulb.png"
                alt="Innovative ideas"
                width={264}
                height={247}
                className="w-[264px] h-[247px] md:w-[393px] md:h-[367px]"
              />
            </div>
          </div>

          <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between items-center gap-3 md:gap-8 mb-12">
            <div className="flex-1">
              <Image
                src="/Images/dev_reuse.png"
                alt="AI and DevOps"
                width={282}
                height={266}
                className="w-[282px] h-[266px] md:w-[395px] md:h-[373px]"
              />
            </div>
            <div className="flex-1 text-justify">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-bold mb-3 md:mb-6">
                ReOps-a perfect blend of AI and DevOps
              </h2>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                With ReOps, we aim to make your life better and easier by
                combining the 2 main heroes of our organization i.e. AI and
                DevOps.
              </p>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                ReOps integrates with established DevOps practices, enhancing
                them without disruption. It utilizes current infrastructure and
                workflows, ensuring that DevOps practitioners transition
                naturally and gradually.
              </p>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                Utilizing the power of AI, we make sure your specifications
                align perfectly with what we have in store. We combine human
                creativity and AI capabilities to deliver what's best for you.
                Because at Valueans, we don't just code, we create-a thoughtful,
                durable, and reliable solution tailored to your needs.
              </p>
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-between items-center gap-8 mb-12">
            <div className="flex-1 mb-12 text-justify ">
              <h2 className="text-center md:text-left text-xl md:text-2xl text-[#232536] font-bold mb-3 md:mb-6 ">
                Accelerate development with ReOps-enabled code reuse
              </h2>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                ReOps ensures enhanced scalability, maintainability, and
                flexibility for your applications.
              </p>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                By embracing and extending what's already out there, ReOps
                allows a smooth and natural adoption for DevOps practitioners,
                empowering you to innovate with confidence. So develop like
                never before!
              </p>
              <p className="mb-1 md:mb-4 text-[#232222] text-sm md:text-lg">
                Because we believe in harnessing the power of existing
                solutions.
              </p>
            </div>

            <div className="flex flex-1 justify-end">
              <Image
                src="/Images/accelate.png"
                alt="Accelerate development"
                width={260}
                height={270}
                className="w-[260px] h-[270px] md:w-[400px] md:h-[300px]"
              />
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default HomeP2;
