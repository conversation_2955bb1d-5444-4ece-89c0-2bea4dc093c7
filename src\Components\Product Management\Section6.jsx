import React from "react";
import Card from "../App Integration/Card";

const Card_1 = ({ title, description }) => {
  return (
    <div className="block bg-white w-full md:w-[400px] h-auto md:h-[474px] p-4 border border-purple-500 shadow-sm rounded-md">
      <h3 className="w-[90%] md:w-[80%] text-lg md:text-2xl font-semibold mb-2 md:mb-4">
        {title}
      </h3>
      <p className="text-base md:text-xl">{description}</p>
    </div>
  );
};

const Card_2 = ({ title, description }) => {
  return (
    <div className="block bg-white w-full md:w-[411px] h-auto md:h-[350px] p-4 border border-purple-500 shadow-sm rounded-md">
      <h3 className="text-lg md:text-2xl font-semibold mb-2 md:mb-4">
        {title}
      </h3>
      <p className="text-base md:text-xl">{description}</p>
    </div>
  );
};

const Section6 = () => {
  return (
    <div className="w-[75%] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        <span className="text-[#7716BC]">How We Work:</span> The Product <br />{" "}
        Management Services Process
      </h2>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-5">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 my-3 md:my-5">
          <Card
            title={"1. Strategic Product Consulting Services"}
            description={
              "Our service includes aiding you in designing a product strategy that works to your advantage, which is why you get not only consulting services but rather product strategy services. We facilitate defining your market’s unique values with you so that together we can differentiate and position your product. "
            }
            width={"w-[300px]"}
            height={"md:h-[500px]"}
            headingFont={"md:text-lg text-base text-[#232222]"}
          />
          <Card
            title={"2. Product Development and Design"}
            description={
              "At Valueans, we put so much emphasis on building long-lasting and extensible products which are user centric. All our designers and developers liaise with you so that every product is crafted appropriately from both function and form perspectives."
            }
            width={"w-[300px]"}
            height={"md:h-[500px]"}
            headingFont={"md:text-lg text-base text-[#232222]"}
          />
          <Card
            title={"3. Next-Gen Product Management Software Development"}
            description={
              "Managing product development should not be a tiresome endeavor, so we provide you with the tools for it “throughout the entire lifecycle” with utmost simplicity. Our software solutions offer advanced technology to remove hitches in collaborative task management, progress tracking, and performance analysis, thus enhancing workflows and focus during product development."
            }
            width={"w-[300px]"}
            height={"md:h-[500px]"}
            headingFont={"md:text-lg text-base text-[#232222]"}
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6 my-3 md:my-5">
          <Card
            title={"4. Market Expansion Strategies"}
            description={
              "Entering new markets can be challenging, however, with our support, we will help you navigate the global market seamlessly. We lower the barrier to entry for their businesses by filling in the gaps within their product portfolio which enables them to regulate compliance, target growth and scale their business."
            }
            width={"w-[300px]"}
            height={"md:h-[500px]"}
            headingFont={"md:text-lg text-base text-[#232222]"}
          />
          <Card
            title={"5.Post-Launch Growth and Optimization"}
            description={
              "It is at this stage that we evaluated a client’s first expectations. Their growth, from this point, will be with our support and assistance. This value-serving involves evaluating the product being used, engagement metrics, and analyzing data needed to enhance functionality features of the product."
            }
            width={"w-[300px]"}
            height={"md:h-[500px]"}
            headingFont={"md:text-lg text-base text-[#232222]"}
          />
        </div>
      </div>
    </div>
  );
};

export default Section6;
