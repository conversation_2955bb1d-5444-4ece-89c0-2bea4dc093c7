import React from "react";
import Tab from "@/Components/Portfolio/Tab";
import Hybrid from "@/Components/Portfolio/Hybrid";

const HybridPortfolio = () => {
  return (
    <section className="bg-white">
      <div className="max-w-[85vw] mx-auto py-12">
        <div className="w-[70vw] mx-auto">
          <h2 className="text-4xl text-center text-[#F245A1] font-semibold">
            Hybrid App Portfolio
          </h2>
          <p className="text-center text-lg font-normal mt-10">
            Discover our collection of hybrid applications that combine the best of web and native technologies.
            Our hybrid solutions offer cross-platform compatibility with native-like performance,
            providing cost-effective solutions without compromising on user experience.
          </p>
        </div>
        <div className="max-w-fit mx-auto my-14">
          <Tab />
        </div>
        
        
      </div>
      <Hybrid />
    </section>
  );
};

export default HybridPortfolio;
