"use client";
import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";

import Image from "next/image";
import Heading from "./Heading/Heading";

const IndustryExpertiseCard = ({ imageSrc, altText, title, description }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect screen width to determine if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const toggleExpansion = () => {
    if (isMobile) {
      setIsExpanded((prev) => !prev);
    }
  };

  return (
    <div className="relative max-w-sm h-[30vh] md:h-[50vh] mx-auto my-10">
      <div className="relative w-full h-[200px]">
        <Image src={imageSrc} alt={altText} layout="fill" objectFit="cover" />
      </div>
      <div
        onClick={toggleExpansion}
        className="absolute top-[89px] left-[16px] max-w-[calc(100%-32px)] p-4 bg-white rounded-lg shadow transition-all duration-300 hover:z-10 hover:bg-white hover:shadow-lg"
      >
        <h5 className="mb-1 text-sm md:text-lg font-bold tracking-tight text-gray-900">
          {title}
        </h5>
        <p
          className={`text-sm text-gray-600 transition-all duration-300 ${
            isMobile
              ? isExpanded
                ? "line-clamp-none"
                : "line-clamp-4"
              : "line-clamp-4 hover:line-clamp-none hover:overflow-auto"
          }`}
        >
          {description}
        </p>
      </div>
    </div>
  );
};

const expertiseData = [
  {
    imageSrc: "/Images/expertise1.png",
    altText: "Healthcare",
    title: "Healthcare",
    description:
      "The healthcare industry requires precision, security, and efficiency to manage patient data and streamline operations. Valueans delivers tailored solutions to enhance care, optimize workflows, and ensure regulatory compliance for better outcomes and smoother operations.",
  },
  {
    imageSrc: "/Images/expertise2.png",
    altText: "Logistics",
    title: "Logistics",
    description:
      "In logistics, efficiency and real-time tracking are crucial for smooth supply chain operations. Valueans delivers innovative solutions to enhance route planning, inventory tracking, and real-time analytics. Our logistics experts optimize your logistics network for maximum efficiency and scalability.",
  },
  {
    imageSrc: "/Images/expertise3.png",
    altText: "Travel",
    title: "Travel",
    description:
      "The travel industry thrives on seamless booking experiences and personalized customer solutions. Valueans offers technology platforms that ensure user-friendly experiences, optimize booking systems, and enhance overall efficiency while delivering exceptional travel services and managing operations effectively.",
  },
  {
    imageSrc: "/Images/expertise4.png",
    altText: "Construction",
    title: "Construction",
    description:
      "The construction industry benefits from innovative project management tools, ensuring seamless collaboration. Valueans provides solutions for project planning, resource allocation, safety compliance, and reducing costs, ensuring your construction projects are completed on time and within budget.",
  },
  {
    imageSrc: "/Images/expertise5.png",
    altText: "Manufacturing",
    title: "Manufacturing",
    description:
      "With over 6 years of experience in providing on-demand solutions, we deliver solutions that optimize production processes, improve quality controls, and refine supply chain visibility. Helping your business thrive by reducing downtime and enhancing output efficiency.",
  },
  {
    imageSrc: "/Images/expertise6.png",
    altText: "Agriculture",
    title: "Agriculture",
    description:
      "Valueans offers smart solutions for precision agriculture, farm management, and data analytics, helping you streamline agricultural processes. Our solutions empower you to optimize yields and operations with advanced technology and customized services.",
  },
  {
    imageSrc: "/Images/expertise7.png",
    altText: "Oil and Gas",
    title: "Oil and Gas",
    description:
      "The oil and gas industry requires robust systems for exploration, production, and delivery while enhancing safety and reducing costs. Valueans provides advanced tools to monitor operations, improve safety standards, and support your efforts to manage complex operations and drive innovation.",
  },
];

export default function IndustryPage() {
  return (
    <div className="md:mb-10">
      <Heading>
        Our <span className="text-[#7716BC]">Industry</span> Expertise
      </Heading>
      <div className="relative px-4 flex flex-col md:flex-row justify-center items-center">
        <Swiper
          // Default to 1 slide per view (mobile)
          slidesPerView={1}
          spaceBetween={30}
          grabCursor={true}
          pagination={{ clickable: true }}
          modules={[Pagination]}
          className="w-full h-full relative pb-10"
          // Display 3 slides on larger screens
          breakpoints={{
            768: {
              slidesPerView: 3,
            },
          }}
        >
          {expertiseData.map((item, index) => (
            <SwiperSlide key={index}>
              <IndustryExpertiseCard {...item} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
