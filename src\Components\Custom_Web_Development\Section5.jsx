import Image from "next/image";

const cardData = [
  {
    title: "Retainer for General Services",
    description:
      "This engagement model is perfect for companies that want consistent, dependable cross-workflow assistance. In exchange for a set cost, you receive technical support and help to guarantee your systems operate faultlessly around-the-clock.",
  },
  {
    title: "Flexible Bulk Buying Options",
    description:
      "This approach is perfect for companies with varying service requirements since it enables customers to buy hours or bulk services in advance at a predetermined fee. When necessary, they can make use of these services or service hours.",
  },
  {
    title: "Dedicated Team Model",
    description:
      "This paradigm works well for long-term projects that need constant improvement, testing, and upkeep. By coordinating with the company's objectives and procedures, the dedicated development team serves as an extension of the client's internal team.",
  },
  {
    title: "Support & Maintenance Contract",
    description:
      "Valueans contract offers ongoing maintenance and support for the systems and apps used by current clients. Perfect for companies that require frequent updates, technical assistance, and troubleshooting to guarantee faultless operation around-the-clock.",
  },
  {
    title: "IT Staff Augmentation",
    description:
      "Companies might hire a service provider to provide more resources to their own staff. Without having to hire full-time staff, clients may scale up their teams in response to seasonal needs.",
  },
  {
    title: "Fixed Budget Model",
    description:
      "A written contract between the customer and the service provider validates their agreement on a certain scope and fee. Project scope modifications are incorporated as they happen, as are adjustments to the budget and schedule.",
  },
];

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full border border-pink-500 p-2 md:p-4 rounded-md shadow">
      <div className="flex items-center gap-2">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Icon"
            width={32}
            height={32}
            className="w-full h-full"
          />
        </div>
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-sm md:text-base text-justify mt-2">{description}</p>
    </div>
  );
};

const Section5 = () => {
  return (
    <div className="bg-blue-100 p-3 md:px-[75px] md:py-[42px]">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        What Makes <span className="text-[#F245A1]"> Valueans</span> Web <br />{" "}
        Development Services Special?
      </h2>{" "}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 mt-6 md:mt-[42px]">
        {cardData.map((card, index) => (
          <InfoCard
            key={index}
            title={card.title}
            description={card.description}
          />
        ))}
      </div>
    </div>
  );
};

export default Section5;
