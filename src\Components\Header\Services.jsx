"use client";
import React from "react";
import Link from "next/link";

const Services = () => {
  return (
    <div className="container mx-auto p-10 border rounded-lg">
      <div className="grid grid-cols-6 gap-4 ">
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold"> Custom Software Solutions</h4>
            <Link
              href="/Software_Development"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Software Development
            </Link>
            <Link
              href="/Custom_Web_Development"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Custom Web Development
            </Link>
            <Link
              href="/Saas"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Saas Development
            </Link>
            <Link
              href="/Full_Stack_Development_Services"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Full Stack Development
            </Link>
            {/* <Link
              href="/Web-App-Development"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Web App Development
            </Link> */}
            <Link
              href="/Mobile-App-development"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Mobile App Development
            </Link>
            <Link
              href="/Fintech"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Fintech
            </Link>
            
            <Link
              href="/HealthCare"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Healthcare Development
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">IT Consulting</h4>
             <Link
              href="/AI"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              AI
            </Link>
            <Link
              href="/App_Integration"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Application Integration
            </Link>
             <Link
              href="/Cloud_Services"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Cloud Services
            </Link>
            <Link
              href="/Business_Intelligence"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Business Intelligence
            </Link>
            <Link
              href="/Product_Management"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Product Management
            </Link>
            <Link
              href="/ML"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              ML
            </Link>
            {/* <Link
              href="/Saas"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Saas Development
            </Link> */}
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Testing & QA</h4>
            <Link
              href="/Quality_Assurance"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Quality Assurance & Testing
            </Link>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Data Solutions</h4>
            <Link
              href="/Data_and_Analytics"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Data Analytics
            </Link>
            <Link
              href="/DataEngineering"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Data Engineering
            </Link>
           
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Design Services</h4>
            <Link
              href="/UI_UX"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              UI UX designing
            </Link>
            
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Application Services</h4>
            <Link
              href="/Maintenance_and_Support"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Maintainence and Support
            </Link>
            <Link
              href="/Dedicated_Deployment_teams"
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline"
            >
              Dedicated Deployment Teams
            </Link>
            
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
