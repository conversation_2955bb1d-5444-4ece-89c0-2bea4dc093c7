import { HelpCircleIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import ProjectsRow from "./ProjectsRow";

const Portfolio_Featured = () => {
  return (
    <section className="">
      <ProjectsRow
        image={"/Images/PaintReady.png"}
        Heading={"Paint Ready"}
        paragrph={
          "At Valueans, our team of dedicated developers has successfully delivered enterprise software development solutions to many clients.  One of the most prominent projects is Paint Ready which was designed......"
        }
        link={"/Portfolio/web-app/PaintReady"}
      />
      <ProjectsRow
        image={"/Images/Ekoj.png"}
        Heading={"EKOJ"}
        paragrph={
          "Our client required us to develop an e-commerce SaaS product for selling medicines online. The major purpose of this SaaS..."
        }
        link={"/Portfolio/web-app/Ekoj"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/AlcoholMonitor.png"}
        Heading={"Ohio Alcohol Monitoring Systems"}
        paragrph={
          "Valueans specializes in developing software solutions that aim to boost business profitability by streamlining operations and improving efficiency. Recently we developed Ohio Alcohol Monitoring Systems ....."
        }
        link={"/Portfolio/web-app/OhioAlcoholMonitoring"}
      />
      <ProjectsRow
        image={"/Images/IMS.png"}
        Heading={"IMS Management System"}
        paragrph={
          "IMS, a Philippines-based inventory management system designed to help businesses streamline their inventory processes. This production is design for company name IMS internal use.We provided easy-to-use...."
        }
        link={"/Portfolio/web-app/IMS"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/HealthCarep.png"}
        Heading={"Health Care System"}
        paragrph={
          "The Health Care System is one of our most successful projects that has helped a hospital improve its management and operations. Due to the nature of the healthcare industry, we had to be very particular...."
        }
        link={"/Portfolio/web-app/HealthCare"}
      />
      <ProjectsRow
        image={"/Images/BolehLand.png"}
        Heading={"BOLEHLAND"}
        paragrph={
          "Enbarr is designed for the horse community where horse enthusiasts can easily buy and sell horses online. This platform provides a user-friendly interface for horse owners and breeders to list their horses ..."
        }
        link={"/Portfolio/web-app/BolehLand"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/Akatsuki.png"}
        Heading={"Akatsuki-opvdata"}
        paragrph={
          "A fintech project, which collects data for trading and creates graphs to help investors make informed decisions. Our platform utilizes advanced data analytics and machine learning algorithms to collect, ......"
        }
        link={"/Portfolio/web-app/Akatsuki"}
      />
      <ProjectsRow
        image={"/Images/Enbar.png"}
        Heading={"Enbarr"}
        paragrph={
          "At Valueans, we aim to deliver unique digital solutions designed to meet clients’ specific business needs. Our solution brings communities together. One innovative example is Enbarr, which is an online ...."
        }
        link={"/Portfolio/web-app/Enbarr"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/GTRLoan.png"}
        Heading={"GTR LOAN"}
        paragrph={
          "Another FinTech product created by Valueans is GTR LOAN, which provides a simple and rapid application procedure for borrowing money. Users can get money using this app without having to go through ..."
        }
        link={"/Portfolio/web-app/GTR"}
      />
      <ProjectsRow
        image={"/Images/Rafiq.png"}
        Heading={"RAFIQ"}
        paragrph={
          "Valueans has provided digital solutions to several industries. One of the latest projects we delivered is RAFIQ which is an Artificial Intelligence solution. The main purpose of developing RAFIQ was to bring...."
        }
        link={"/Portfolio/web-app/Rafiq"}
        classname={"md:flex-row-reverse"}
      />
      <ProjectsRow
        image={"/Images/JobWorth.png"}
        Heading={"JOBWORTH"}
        paragrph={
          "Our clients wanted a platform where job seekers and employers could connect. They asked us to deliver a solution where both parties can create their profiles and reach out to each other....."
        }
        link={"/Portfolio/web-app/JobWorth"}
      />
      <ProjectsRow
        image={"/Images/HBS.png"}
        Heading={"HealthCare Billing System"}
        paragrph={
          "Valueans has provided digital solutions to several businesses helping them to streamline their operations and processes that result in their growth. We’ve dealt with many industries and healthcare is ......"
        }
        link={"/Portfolio/web-app/HealthCareBilling"}
        classname={"md:flex-row-reverse"}
      />
    </section>
  );
};

export default Portfolio_Featured;
