import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import React from "react";

const ProjectsRow = ({ image, Heading, paragrph, link, classname }) => {
  return (
    <div>
      <div
        className={`flex flex-col ${classname ? classname : "md:flex-row"} justify-between items-center gap-4`}
      >
        <div className="md:w-[50%]">
          <Image src={image} alt="Paint Ready" width={739} height={484} />
        </div>
        <div className="md:w-[50%] px-5">
          <h2 className="text-3xl font-semibold">{Heading}</h2>
          <p className="text-base font-light mt-2 mb-10">{paragrph}</p>
          <Link
            href={link}
            className={`flex items-center ${classname ? "justify-start" : "justify-end"} mr-14 text-purple-700 text-lg `}
          >
            <span>Explore</span>
            <FiArrowRight />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ProjectsRow;
