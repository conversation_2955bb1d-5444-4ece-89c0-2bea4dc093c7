"use client";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function ExploreServices() {
  // Route mapping for bullet points
  const routeMapping = {
    "Custom Software Development": "/Software_Development",
    "SaaS Development": "/Saas",
    "Full Stack Development": "/Full_Stack_Development_Services",
    "Custom Website Development": "/Custom_Web_Development",
    "Financial App Development": "/Fintech",
    "Dedicated Development Teams": "/Dedicated_Deployment_teams",
    "Healthcare Development": "/HealthCare",
    "Cloud Application Development": "/Cloud_Services",
    "Technology Strategy": "/Product_Management",
    "System Integration": "/App_Integration",
    "Cloud Solutions": "/Cloud_Services",
    "IT Infrastructure Consulting": "/Product_Management",
    "Quality assurance and testing": "/Quality_Assurance",
    "AI": "/AI",
    "ML": "/ML",
    "Business Intelligence": "/Business_Intelligence",
    "Data Engineering": "/DataEngineering",
    "Cloud Services": "/Cloud_Services",
    "Generative AI": "/AI",
    "UIUX Design": "/UI_UX",
    "Mobile First Design": "/UI_UX",
    "Mobile Application Development": "/Mobile-App-development",
    "Application Integration": "/App_Integration",
  };

  // Content data
  const contentData = {
    "Software Development": {
      heading: "Software Development",
      paragraph:
        "At Valueans, we specialize in creating reliable, scalable, and secure software solutions tailored to meet your business goals. With years of experience in delivering top-notch software across all platforms—OS, browser, and device—we combine deep industry knowledge with latest technology trends to build custom solutions and products that perfectly align with your users’ needs and preferences.",
      bulletPoints: [
        "Custom Software Development",
        "SaaS Development",
        "Full Stack Development",
        "Custom Website Development",
        "Financial App Development",
        "Dedicated Development Teams",
        "Healthcare Development",
        "Cloud Application Development",
      ],
    },
    "IT Consulting": {
      heading: "IT Consulting",
      paragraph:
        "Our IT consulting services provide strategic guidance to businesses looking to optimize their technology stack and processes.",
      bulletPoints: [
        "Technology Strategy",
        "System Integration",
        "Cloud Solutions",
        "IT Infrastructure Consulting",
      ],
    },
    "Testing & QA": {
      heading: "Testing & QA",
      paragraph:
        "We provide comprehensive QA and testing outsourcing services, including building or enhancing your QA processes and supporting TCoE setup and advancement. Our team performs end-to-end testing for mobile, web, and desktop applications at every stage of the development lifecycle, ensuring quality and reliability.",
      bulletPoints: ["Quality assurance and testing"],
    },
    "Data Analytics": {
      heading: "Data Analytics",
      paragraph:
        "We empower businesses to make data-driven decisions by transforming historical and real-time data—both traditional and big data—into actionable insights. Our services prepare your data and infrastructure for analysis, equipping your organization with advanced analytics capabilities to drive smarter strategies and stronger outcomes.",
      bulletPoints: [
        "AI",
        "ML",
        "Business Intelligence",
        "Data Engineering",
        "Cloud Services",
        "Generative AI",
      ],
    },
    "Design Services": {
      heading: "Design Services",
      paragraph:
        "We empower businesses to make data-driven decisions by transforming historical and real-time data—both traditional and big data—into actionable insights. Our services prepare your data and infrastructure for analysis, equipping your organization with advanced analytics capabilities to drive smarter strategies and stronger outcomes.",
      bulletPoints: ["UIUX Design", "Mobile First Design"],
    },
    "Application Services": {
      heading: "Application Services",
      paragraph:
        "Our experts support mid-sized and large firms in building, testing, securing, managing, migrating, and optimizing digital solutions, ensuring they run smoothly and achieve the best total cost of ownership (TCO).",
      bulletPoints: [
        "Mobile Application Development",
        "Application Integration",
      ],
    },
  };

  // State to manage active option
  const [activeOption, setActiveOption] = useState("Software Development");

  // State for accordion open status on smaller screens
  const [openAccordion, setOpenAccordion] = useState("");

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setOpenAccordion(""); // Close the accordion on larger screens
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Call it once to ensure the state is correct on initial render

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className=" max-w-[80%] mx-auto relative flex flex-col md:flex-row">
      {/* Left Sidebar */}
      <div className="bg-[#7716BC] w-full md:w-1/3 p-8 md:h-[715px] shadow-lg">
        {Object.keys(contentData).map((option) => (
          <div
            key={option}
            onClick={() => {
              if (typeof window !== "undefined" && window.innerWidth < 768) {
                // Accordion for smaller screens
                setOpenAccordion((prev) => (prev === option ? "" : option));
              } else {
                // Standard behavior for larger screens
                setActiveOption(option);
              }
            }}
            className={`p-4 cursor-pointer text-white  text-xl font-medium mt-8 ${
              (typeof window !== "undefined" &&
                window.innerWidth < 768 &&
                openAccordion === option) ||
              (typeof window !== "undefined" &&
                window.innerWidth >= 768 &&
                activeOption === option)
                ? "bg-purple-600"
                : "hover:bg-[#C67FF9]"
            }`}
          >
            {option}

            {/* Accordion content for smaller screens */}
            <div
              className={`transition-all duration-300 overflow-hidden ${
                typeof window !== "undefined" &&
                openAccordion === option &&
                window.innerWidth < 768
                  ? "max-h-[1000px] bg-white text-black p-4 rounded-lg shadow-md mt-4"
                  : "max-h-0"
              }`}
            >
              {typeof window !== "undefined" &&
                openAccordion === option &&
                window.innerWidth < 768 && (
                  <div>

                    <p className="mb-4 text-sm">{contentData[option].paragraph}</p>
                    <ul className="list-disc list-inside">
                      {contentData[option].bulletPoints.map((point, index) => (
                        <li key={index} className="mt-2 text-sm">
                          {routeMapping[point] ? (
                            <Link
                              href={routeMapping[point]}
                              className="text-gray-800 hover:text-[#7716BC] hover:underline cursor-pointer transition-colors duration-200"
                            >
                              {point}
                            </Link>
                          ) : (
                            point
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </div>
        ))}
      </div>

      {/* Right Content Panel */}
      <div className="hidden md:block bg-white w-full md:w-2/3 p-8 shadow-lg">
        <h2 className=" text-left mb-3 md:mb-8 mt-2  mx-auto p-2 text-2xl md:text-2xl md:leading-normal text-[#232536] font-semibold underline underline-offset-2 decoration-purple-600 md:px-[30px]">
          {contentData[activeOption].heading}
        </h2>
        <p className="text-[#232222] text-sm md:text-lg md:leading-[30px] px-4 md:px-[30px]">
          {contentData[activeOption].paragraph}
        </p>

        {/* Bullet Points Grid */}
        <div className="grid grid-cols-2 gap-y-6 gap-x-10 mt-6 md:px-[30px]">
          {contentData[activeOption].bulletPoints.map((point, index) => (
            <div key={index} className="flex items-center">
              {/* Red Arrow Symbol */}
              <span className="text-red-500 mr-2 text-lg">›</span>

              {/* Bullet Text */}
              {routeMapping[point] ? (
                <Link
                  href={routeMapping[point]}
                  className="font-semibold text-sm md:text-lg md:leading-[30px] text-gray-800 hover:text-[#7716BC] hover:underline cursor-pointer transition-colors duration-200"
                >
                  {point}
                </Link>
              ) : (
                <span className="font-semibold text-sm md:text-lg md:leading-[30px] text-gray-800">
                  {point}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
