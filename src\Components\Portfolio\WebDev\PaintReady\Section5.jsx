"use client";

import React from "react";
import Image from "next/image";

export default function Section5({ images }) {
  return (
    <div>
      
      <h1 className="text-3xl font-semibold text-center my-5 text-[#F245A1]">What was delivered?</h1>
    <div className="w-full overflow-hidden px-4">
      <div
        className="
          flex gap-6
          overflow-x-auto
          snap-x snap-mandatory
          scrollbar-hide
          
        "
      >
        {images.map((img, index) => (
          <div
            key={index}
            className="
              snap-center
              shrink-0
              w-[90%]
              rounded-xl border shadow-lg
              transition-transform duration-300
            "
          >
            <Image
              src={img}
              alt={`Slide ${index + 1}`}
              width={1600}
              height={900}
              className="w-full h-full object-contain rounded-xl"
            />
          </div>
        ))}
      </div>
      </div>
      <h3 className="text-2xl font-semibold my-5 text-center">Web Application</h3>
    </div>
  );
}
