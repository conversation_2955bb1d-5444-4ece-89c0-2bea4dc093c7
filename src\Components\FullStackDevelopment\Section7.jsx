import React from "react";
import Card from "./Card_2";

const Section7 = () => {
  const cardData = [
    {
      title: "Healthcare and Biotech",
      content: [
        "Patient Management Systems",
        "Telemedicine Platforms",
        "Healthcare Data Integration",
        "Predictive Analytics for Treatment",
        "Medical Software Development",
      ],
    },
    {
      title: "Retail Industry",
      content: [
        "E-commerce Platforms",
        "Customer Relationship Management (CRM)",
        "Inventory Management Systems",
        "Payment Gateway Integration",
        "Product Recommendation Systems",
      ],
    },
    {
      title: "Legal & Business Solutions",
      content: [
        "Custom Legal Platforms",
        "Document Automation",
        "Compliance Management Tools",
        "Contract Review Systems",
        "Legal Research Tools",
      ],
    },
    {
      title: "IT Services",
      content: [
        "End-to-End Web Development",
        "API Integration",
        "Cloud Solutions",
        "Scalable Architecture",
        "Custom Software Solutions",
      ],
    },
  ];

  const cardData_2 = [
    {
      title: "Energy and Resources",
      content: [
        "Smart Energy Management",
        "Predictive Maintenance",
        "Data Analytics for Resource Management",
        "IoT Integrations for Monitoring",
        "Workflow Automation",
      ],
    },
    {
      title: "Manufacturing Industry",
      content: [
        "Process Automation Solutions",
        "Custom ERP Systems",
        "Supply Chain Optimization",
        "Predictive Maintenance Platforms",
        "Robotics Integration for Manufacturing",
      ],
    },
    {
      title: "Real Estate Solutions",
      content: [
        "Property Management Software",
        "Real Estate Market Analysis Tools",
        "Smart Building Systems",
        "Predictive Maintenance Systems",
        "Energy Efficiency Solutions",
      ],
    },
  ];

  return (
    <div className="w-[85%] mx-auto my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        Valueans Successfully{" "}
        <span className="text-[#F245A1]">Delivers Full Stack Development</span>{" "}
        Across Various Industries
      </h2>
      <div className="flex flex-col md:flex-row justify-center items-center gap-5 my-5">
        {cardData.map((card, index) => (
          <Card
            key={index}
            title={card.title}
            content={card.content}
            className="border-purple-200"
          />
        ))}
      </div>
      <div className="flex flex-col md:flex-row justify-center items-center gap-5">
        {cardData_2.map((card, index) => (
          <Card
            key={index}
            title={card.title}
            content={card.content}
            className="border-purple-200"
          />
        ))}
      </div>
    </div>
  );
};

export default Section7;
