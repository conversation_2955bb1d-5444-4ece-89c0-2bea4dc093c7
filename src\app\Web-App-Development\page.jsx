import React from "react";
import Banner from "@/Components/Services/Banner";
import PurpleDiv from "@/Components/Homepage/HomeP3";
import Web_Services from "@/Components/Web_dev/Web_Services";
import Web_features from "@/Components/Web_dev/Web_features";
import Devprocess from "@/Components/Web_dev/Web_dev_process";
import FeaturedProject from "@/Components/Services/FeaturedProject";
import HomeP8 from "@/Components/Homepage/HomeP8";
import Faq from "@/Components/Faq/Faq";
import ServiceDescription from "@/Components/Services/ServiceDescription";
const Web_App_Development_page = () => {
  return (
    <div>
      <Banner
        title="Service to Accelerate Your Venture."
        titleHighlight="Web Application Development"
        description="The modern way to build your "
        descriptionHighlight="presence"
        Imagesrc={"/Images/web_dev.png"}
        ImageAlt={"Web app development"}
        highlightColor="#F245A1"
        titleColor="#FFFFFF"
      />
      <ServiceDescription
        primaryText={
          "Approximately 73% of businesses have a website that helps them build their brand online. However, not all companies achieve what they want because of poor SEO or non-responsive designs of off-the-shelf websites. "
        }
        primaryTextHighlight={"."}
        secondaryText={
          "Valueans robust and scalable development solutions, ready-to-deliver team, and personalized ecosystem are what you need to stand out in the market."
        }
      />
      <PurpleDiv>
        Your Trusted Web Design and Development Partner/The Trusted Web <br />
        Development Partner You’ve Been Looking For
      </PurpleDiv>
      <Web_Services />
      <Web_features />

      <FeaturedProject />
      <HomeP8 />
      <Faq />
    </div>
  );
};

export default Web_App_Development_page;
