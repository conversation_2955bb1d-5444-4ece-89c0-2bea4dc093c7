const Card = ({ title, description }) => {
  return (
    <div className="block bg-white w-full md:max-w-sm md:h-[320px] p-4 shadow-md rounded-xl overflow-hidden">
      <h3 className="text-base md:text-lg text-[#7716BC] capitalize font-semibold mb-2 ">
        {title}
      </h3>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section4 = () => {
  return (
    <div className="w-[85vw] mx-auto mb-10 md:mb-24">
      <h2 className="text-2xl md:text-[38px] md:leading-[57px] text-center font-semibold">
        <span className="text-[#F245A1]">
          Mobile App Solutions & Development s
        </span>{" "}
        <br /> Services at Valuean
      </h2>

      <div className="flex flex-col justify-center items-center gap-4 md:gap-[18px] mt-6 md:mt-[42px]">
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card
            title={"iOS App Development"}
            description={
              "Let's work together to design a custom iOS app that is suited to the Apple environment. Our team creates native iOS apps that provide a smooth experience and make the most of the device's capabilities. We'll create a product keeping a human centered approach throughout the product management phase. Making sure it works well on all iPhones, regardless of your vision."
            }
          />
          <Card
            title={"Android App Development"}
            description={
              "In order to fully use the Android environment, Valueans creates native Android apps. We develop dependable, responsive applications that expand with your company while adhering to platform-specific design, security, and performance requirements."
            }
          />
          <Card
            title={"Consulting Services for Mobile Apps"}
            description={
              "To begin the creation of your mobile app, we do a thorough study of your company's requirements and industry trends in order to create a backup plan. If you already have an app, we evaluate it and make specific recommendations to improve its performance. "
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card
            title={"Cross-platform Mobile Development"}
            description={
              "Cross-platform applications get around compatibility issues and increase market reach. We create top-notch apps with flawless user experiences for iOS and Android using Flutter and React Native. These provide improved performance, simpler maintenance, and quicker development. "
            }
          />
          <Card
            title={"Mobile App Design"}
            description={
              "Our app design and development services guarantee that the general flow, navigation, interfaces, and aesthetics of your product align with the identity and vision of your company. To guarantee smooth and intuitive user experience, we employ wireframes, prototypes, and user testing. "
            }
          />
          <Card
            title={"Upgrading Existing Apps"}
            description={
              "We propose necessary updates and new features for your mobile app based on our thorough market research, which identifies growing trends in your sector. Our professionals improve the functionality and efficiency of your app to keep your audience interested and informed.  "
            }
          />
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-[18px]">
          <Card
            title={"QA and Software Testing"}
            description={
              "To guarantee that your app offers seamless, responsive, and crash-free experience on all devices, our knowledgeable professionals conduct thorough quality assurance and usability testing. Our advanced knowledge of app development allows us to identify and fix any technical problems fast."
            }
          />
          <Card
            title={"Mobile App Maintenance"}
            description={
              "Every app we create at Valueans is handled as though it were our own. We offer post-deployment maintenance, app enhancements, and customized feature upgrades to guarantee your app's continued performance and dependability. All of these services are designed to maintain your app's long-term quality."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section4;
