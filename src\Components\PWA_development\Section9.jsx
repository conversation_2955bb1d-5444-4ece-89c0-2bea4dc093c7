import Image from "next/image";
import Button from "../Buttons/Button";

const Section9 = () => {
  return (
    <section className=" bg-[#7716BC] py-6 md:py-[42px] md:px-[75px]">
      <div className="flex flex-col-reverse md:flex-row justify-center md:justify-between  gap-5 md:gap-10   items-center ">
        <div className="w-full md:w-[40%] px-8">
          <h2 className="text-center md:text-left text-xl md:text-[28px] text-white font-semibold mt-2 md:mt-0">
            Build the Future with a High-Performance <br /> PWA
          </h2>
          <p className="w-full  text-base md:text-lg  text-white font-normal m-1 mb-10">
            Turning your website into a powerful, app-like experience that keeps
            users engaged, boosts conversions, and works flawlessly across all
            devices. With Valueans, you get a custom-built Progressive Web App
            designed for speed, reliability, and growth.
          </p>
          <div className="bg-[#F245A124] rounded-sm shadow-sm p-4 md:p-6">
            <h3 className="text-white text-lg md:text-2xl font-semibold mb-1">
              Why Partner with Us?
            </h3>
            <ul className="text-white">
              <li>● Fast, scalable, and secure PWA solutions</li>
              <li>● Offline functionality for uninterrupted access</li>
              <li>● Higher engagement and better conversions</li>
            </ul>
          </div>
          <div className="p-4 md:p-6 bg-[#350668] rounded-sm shadow-sm my-3 md:my-6">
            <p className="text-white text-base md:text-xl">
              Let’s create a next-gen digital experience for your business.
            </p>
          </div>
          <div className="w-fit mx-auto md:mx-0">
            <Button bgColor="bg-[#F245A1]">Connect with us</Button>
          </div>
        </div>
        <div className="px-8">
          <Image
            src="/Images/estimate.png"
            alt="estimate"
            width={400}
            height={400}
          />
        </div>
      </div>
    </section>
  );
};

export default Section9;
