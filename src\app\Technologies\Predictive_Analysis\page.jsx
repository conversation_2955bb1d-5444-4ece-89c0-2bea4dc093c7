import React from "react";
import Section1 from "@/Components/PWA_development/Section1";
import Section2 from "@/Components/PWA_development/Section3";
import Section3 from "@/Components/AI/Section2";
import Section4 from "@/Components/PWA_development/Section5";
import Section5 from "@/Components/MicroServices/Section5";
import Section6 from "@/Components/Nlp/Section9";
import Section7 from "@/Components/PWA_development/Section2";
import Section8 from "@/Components/Nlp/Section7";
import Section9 from "@/Components/PWA_development/Section6";
import Faq from "@/Components/Faq/Faq";

const page = () => {
  const Section5cardContent = [
    {
      content: [
        "Anticipate customer attrition and successfully retain existing clients.",
        "Use machine learning to customize suggestions for every client.",
        "Improve customer marketing and gain a deeper insight into your customers.",
        "Optimize customer lifetime value (LTV) by utilizing predictive analytics and CRM data.",
        "Reduce revenue leakage and maintain competitiveness by optimizing your pricing tactics.",
        "Boost your credit score models' quality.",
        "Respond more quickly and precisely to your business's supply demands.",
        "Consult a predictive analytics specialist to increase the profitability of your marketing strategy.",
      ],
    },
  ];
  const ImageCardData = [
    {
      imgsrc: "/Images/PAS4.jpg",
      altsrc: "Data Science",
      title: "Data Science",
      description:
        "An organized and unstructured approach to gaining insight from many types of data and enhancing commercial value is data science. ",
    },
    {
      imgsrc: "/Images/PAS5.png",
      altsrc: "Data Warehousing",
      title: "Data Warehousing",
      description:
        "Because data warehouse systems are strong, scalable, and reasonably priced, they allow businesses to exchange data with thousands of users. ",
    },
    {
      imgsrc: "/Images/PAS6.jpg",
      altsrc: "Data Analytics",
      title: "Data Analytics",
      description:
        "The practice of analyzing data sets to make inferences about the information using certain tools and systems is known as data analytics.  ",
    },
    {
      imgsrc: "/Images/PAS7.jpg",
      altsrc: "Data Visualization",
      title: "Data Visualization",
      description:
        "The most effective way to tackle this problem is through data visualizations, which facilitates the communication of scientific findings to a diverse range of audiences.  ",
    },
    {
      imgsrc: "/Images/PAS8.png",
      altsrc: "Data Migration",
      title: "Data Migration",
      description:
        "The process of moving data from one storage system to another to preserve pertinent and essential data is known as data migration. ",
    },
    {
      imgsrc: "/Images/PAS9.png",
      altsrc: "Big Data Implementation",
      title: "Big Data Implementation",
      description:
        "Implementing big data enables you to foresee company futures and make decisions based on smart data. ",
    },
    {
      imgsrc: "/Images/PAS10.png",
      altsrc: "Predictive Analytics",
      title: "Predictive Analytics",
      description:
        "A program called Advance Analytics may help you foresee trends, behaviors, events, and more to improve performance. ",
    },
    {
      imgsrc: "/Images/PAS11.jpg",
      altsrc: "IoT Analytics",
      title: "IoT Analytics",
      description:
        "IoT analytics is the use of data analysis tools to enhance facilities' data by establishing connections with the Internet of Things. ",
    },
    {
      imgsrc: "/Images/PAS12.jpg",
      altsrc: "Customer 360",
      title: "Customer 360",
      description:
        "Customer 360 gives you a 360-degree perspective on the data and lets you engage with and support all the customers’ details. ",
    },
    {
      imgsrc: "/Images/PAS12.jpg",
      altsrc: "DevOps",
      title: "DevOps",
      description:
        "DevOps is a collection of procedures that combines software development with IT operations to produce software with superior quality and an enhanced software life cycle. ",
    },
    {
      imgsrc: "/Images/PAS14.jpg",
      altsrc: "Microsoft Power BI",
      title: "Microsoft Power BI",
      description:
        "A business analytics tool called Microsoft Power BI offers business insight and interactive visualizations for creating dashboards and reports.",
    },
    {
      imgsrc: "/Images/PAS15.jpg",
      altsrc: "Qlik Sense & QlikView",
      title: "Qlik Sense & QlikView",
      description:
        "The analytics platform and solution Qlik Sense & QlikView combine data, analysis, and data-driven intelligence to support corporate expansion.",
    },
  ];
  const Section9cardContent = [
    {
      content: [
        "To guarantee a more dependable decision-making process, we aid in selecting the most effective applications of big data and predictive analytics in the client's industry.",
        "You may integrate AI-powered features into your company's operations with our predictive analytics services. ",
        "Gain insightful knowledge from your data, improve performance, avoid cost overruns, and implement fresh, effective tactics. ",
        "Our staff adapts and customizes solutions to your company needs and the type of data you have, all while keeping your requirements and use case in mind.",
      ],
    },
  ];
  const Section6cardContent = [
    {
      title: "With low code mobile app development at Valueans, we: ",
      content: [
        "Customers may make well-informed selections with the aid of predictive analytics.",
        "Real-time solutions can be obtained using predictive analytics. Real-time data ingestion and prompt responses are possible with trained predictive analytics algorithms.",
        "Customers may better grasp complicated issues with the use of predictive analytics. It can help in more quickly and precisely identifying patterns in data.",
        "Businesses may obtain a competitive edge with the use of predictive analytics. Because predictive analytics can more precisely forecast future occurrences, businesses that employ it have a competitive edge over those that don't.",
      ],
    },
  ];
  const pinkBgCardData = [
    {
      title: "Predictive Analytics Consulting",
      description:
        "We are prepared to meet with you and discuss how our machine learning solutions could enhance and improve your predictive analytics services. ",
    },
    {
      title: "Predictive Analytics Software Development",
      description:
        "The team at InData Labs can help you create a cutting-edge, unique bespoke solution using a proprietary model, which simplifies and lowers the cost of development. ",
    },
    {
      title: "Machine Learning Model Development",
      description:
        "To meet your company aims, our team of data science specialists and machine learning engineers can assist you in developing a completely automated model.",
    },
  ];
  const PinkTopCardData = [
    {
      title: "Determine a Problem to Address",
      description:
        "Each query in predictive analytics should have a quantifiable and obvious value. You may use the PADS framework to find a question that addresses important business requirements. Preventing issues, helping people, identifying issues, and simplifying services are what PADS stands for. ",
    },
    {
      title: "Choose and Prepare Data",
      description:
        "We require a dataset that can produce insights to run predictive analytics models. We often employ both fresh data, which is where future predictions are formed, and historical data, which is used to train your predictive algorithm on how to forecast an event. ",
    },
    {
      title: "Involve Others",
      description:
        "We begin by addressing the stakeholders in your company, such as any executives or team leaders who must support the initiative. As advocates, stakeholders may assist in spreading the word about the program. We ensure that cross-functional collaboration is obtained and that your project is positioned for long-term success. ",
    },
    {
      title: "Select Predictive Analytics Models",
      description:
        "Several factors will influence the predictive analytics model that we select. One of these five popular models—the classification model, clustering model, forecast model, outlier’s model, and time series model—will be used in most predictive analytics inquiries. ",
    },
    {
      title: " Bridge the Gap Between Insights and Actions",
      description:
        "We carefully consider how to enable you to act in addition to providing the facts for a predictive analytics project. We must get it to the appropriate people first. Based on the data, we then recommend the next actions, ideally allowing users to take action without ever leaving the application. ",
    },
    {
      title: "  Create Prototypes",
      description:
        "Start with a basic version and distribute it to stakeholders and end users for beta testing. These will be your product's first users, and their input will determine how your predictive analytics solution develops. ",
    },
    {
      title: " Iterate Frequently",
      description:
        "As your project develops, keep in touch with your testing team to discuss developments and take into account fresh input. ",
    },
  ];

  const accordionData = [
    {
      title:
        "How much time does it take to create a model for machine learning?",
      content:
        "Most data analysts complete their first model effectively in a few days. Contrast that with the months required for a typical data science team to manually create unique prediction models. Our generative AI copilot, SQL-based modeling, and automated data preparation tools help analysts expedite the process.  ",
    },
    {
      title: "What is the price of predictive analytics services?",
      content:
        "You can select from one of our packages or allow us to create a price specifically tailored to your requirements. Planning and budgeting will be a lot easier with Valueans as you will know your expenses ahead of time, unlike other programs that can have ambiguous pricing dependent on your computation or consumption. ",
    },
    {
      title: "In what ways is my data protected?",
      content:
        "Which data is put into the cloud environment for model training is up to you. Anything that you don't choose remains in your data center. For model training, no PII is ever needed. Additionally, we take extraordinary precautions to guarantee the security of your data. Please don't hesitate to contact us with any inquiries; we would be pleased to address any particular issues you may have. ",
    },
    {
      title:
        "What connections exist between predictive analytics software and other business and data tools?",
      content:
        "With only a few clicks, you may connect thanks to our pre-built connectors to many of the most popular data and business applications. Please let us know if you require a connection that isn't already on the list. It could be in the works already!",
    },
    {
      title: "What is possible with predictive analytics?",
      content:
        "A subfield of advanced analytics known as predictive analytics uses historical data in conjunction with statistical modeling, data mining, and machine learning to forecast future events. Predictive analytics is used by businesses to look for trends in this data to pinpoint opportunities and hazards.",
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/PAS-bg.png"}
        heading={"Predictive analytics service"}
        bannerText={"Intuitive Experience That Evolves with Your Needs."}
      />
      <Section2
        image={"/images/PAS2.png"}
        paragrapgh={
          "Get uncommon commercial benefits by collaborating with our predictive analytics company. Predictive analytics firm Valueans creates tools to assist you in forecasting consumer behavior and results and steer your organization on the right path. We create robust data models intended to guide more effective company plans and choices."
        }
        heading={"Predictive Analytics Consulting"}
      />
      <Section3
        lefttext={" Predictive Analytics in Software Development "}
        righttext={"With predictive data analytics services at Valueans, we:"}
      />
      <Section4
        cardContent={Section5cardContent || []}
        image={"/Images/PAS3.jpg"}
      />
      <Section5
        ImageCardData={ImageCardData}
        spanHeading={"Predictive Data Analytics"}
        heading={" Services at Valueans "}
      />
      <Section6
        heading={"Why Choose Valueans"}
        spanHeading={"Preditive Analytics"}
        afterSpanHeading={" For business Strategy"}
        cardContent={Section9cardContent || []}
        image={"/Images/PAS16.jpg"}
      />
      <Section7
        leftHeading={"Beneifits Of  "}
        blueHeading={"Predictive Analysis "}
        cardContent={Section6cardContent}
      />
      <Section8
        cardData={pinkBgCardData}
        heading={"What we "}
        spanHeading={"Offer"}
        paragrapgh={
          "To meet their company goals, we assist our clients in using customized tools and obtaining the best solutions. "
        }
      />
      <Section9
        PinkTopCardData={PinkTopCardData}
        PinktopCardheight={"md:h-[320px]"}
        spanLeft={"Low Code "}
        heading={"Process Automation Solutions ayt Valueans"}
      />
      <Faq content={accordionData} />
    </div>
  );
};

export default page;
