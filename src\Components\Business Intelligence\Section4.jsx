import React from "react";
import Card from "./Card";
const Section4 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-bold">
        Key Components of{" "}
        <span className="text-[#F245A1]">Business Intelligence</span>
      </h2>
      <p className="text-base md:text-xl text-center my-1 md:my-3">
        To make a business Intelligence (BI) solution successful, all the components must be in line like clockwork. This will give you a clear picture of where you stand and what to expect. It will make it easy to find opportunities and solve problems quickly. 
      </p>
      <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-3 md:my-6">
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Data Collection and Integration "}
            description={
              "Data collection is the cornerstone of BI solutions. Often time data is not well integrated; it can be scattered and difficult to analyze. BI analyzes systems like ERP, CRM, cloud apps, and IoT deployment services and consolidates all the information in a single format. Ready to be used efficiently. This process removes data silos, making it easier for businesses to use reliable data and perform In-depth analysis. "
            }
            height={"md:h-[300px]"}
          />
          <Card
            title={"Data Warehousing "}
            description={
              "Data warehouses store all your integrated data. It is a stronghold that must be protected at all costs. It provides everyone with consistent/universal data, reducing errors and improving reporting. Today’s data warehouses can even handle unstructured data, making it possible to run advanced analytics and uncover insights across all areas of your business—whether it’s finance, marketing, or supply chain operations."
            }
            height={"md:h-[300px]"}
          />
        </div>
        <div className="flex justify-center items-center p-4">
          <div className="block w-full md:max-w-xl p-8 bg-white border  border-gray-200 rounded-lg shadow-lg">
            <h2 className="text-[#7716BC] mb-2 text-lg md:text-xl font-bold tracking-tight">
              Data Analysis
            </h2>
            <ul style={{ listStyleType: "disc" }} className="text-sm md:text-base">
              <li>
                <span className="font-bold">Descriptive Analytics:</span>
                Analyzes past performance to answer, “What happened?”
              </li>
              <li>
                <span className="font-bold">Diagnostic Analytics:</span>
               It understands “Why something happened?” by spotting patterns and connections.
              </li>
              <li>
                <span className="font-bold">Predictive Analytics:</span> Uses
                past data to predict future outcomes, helping you plan.
              </li>
              <li>
                <span className="font-bold">Prescriptive Analytics:</span>Uses
                ML algorithms to identify patterns and suggest further actions.
              </li>
            </ul>
          </div>
        </div>
        <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
          <Card
            title={"Data Visualization"}
            description={
              "Working with raw data can be a real challenge—it’s disorganized, complex, and difficult to understand. This is where data visualization saves the day. It transforms all those figures into straightforward, easy-to-read visuals such as interactive dashboards, heatmaps, and comprehensible graphs. Suddenly, it becomes easy to spot trends and grasp what’s going on, leading to quicker decision-making. The most amazing part? With custom dashboards, every team can concentrate on what is most important to them, eliminating distractions and maintaining focus."
            }
          />
          <Card
            title={"Reporting and Performance Monitoring"}
            description={
              "When making sound choices as a company, detailed reporting is critical. BI as a service allows for automated reports to be created on various company metrics that include KPIs and analytics that are up to date demonstrating the extensive BI utilization across organizations. Companies use both high levels of summary and expansion and detailed reports which help them look for and stop growth opportunities, inefficiencies, and risks. Such advanced BI systems allow for ad hoc reporting allowing users to self-service and create reports when they will need them without requesting the IT team."
            }
          />
        </div>
      </div>
    </div>
  );
};

export default Section4;
