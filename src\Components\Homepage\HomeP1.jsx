import React from "react";
import Button from "../Buttons/Button";
import Image from "next/image";

const HomeP1 = () => {
  return (
    <>
      <section
        className="pt-4 md:pt-[34px] px-7 md:px-20 pb-10 md:pb-[100px] bg-cover bg-center bg-no-repeat h-fit md:min-h-screen w-full "
        style={{
          backgroundImage: `url('/Images/HomePage.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="flex flex-col md:flex-row justify-center items-center md:justify-between mt-5">
          <div className="text-center md:text-start space-y-4 md:space-y-20 md:w-[50%]">
            <h1 className="text-white text-4xl md:text-7xl font-trirong">
              Bringing your{" "}
              <span
                className="relative 
                      inline-block 
                      text-[#F245A1] 
                      font-aclonica 
                      font-bold
                      after:content-[''] 
                      after:absolute 
                      after:left-0 
                      after:-bottom-1 
                      after:w-full 
                      after:h-2 
                      after:bg-[url('/Images/underline.png')] 
                      after:bg-contain 
                      after:bg-no-repeat"
              >
                Ideas
              </span>{" "}
              to life
            </h1>

            <div>
              <p className="text-white text-2xl py-1 md:my-4">
                You Explain, We Build
              </p>
              <Button
                bgColor="bg-[#F245A1]"
                hoverColor="opacity-90"
                paddingX=" px-6 md:px-20"
                paddingY="py-1 md:py-2"
              >
                Let&apos;s talk
              </Button>
            </div>
            <div className="flex justify-center md:justify-start gap-2 md:gap-8 mt-4 text-white">
              <div>
                <p className="text-lg font-bold md:text-[32px] font-titillium">
                  20K+
                </p>
                <p className="text-xs md:text-[20px] font-titillium">
                  Projects
                </p>
              </div>
              <div className="pl-2 md:pl-4 border-l-2 border-white">
                <p className="text-lg font-bold md:text-[32px] font-titillium">
                  20K+
                </p>
                <p className="text-xs md:text-[20px] font-titillium">
                  Projects
                </p>
              </div>
              <div className="pl-2 md:pl-4 border-l-2  border-white">
                <p className="text-lg font-bold md:text-[32px] font-titillium">
                  20K+
                </p>
                <p className="text-xs md:text-[20px] font-titillium">
                  Projects
                </p>
              </div>
            </div>
          </div>
          <div className=" md:w-[50%] flex flex-col">
            <div className="hidden md:flex justify-end">
              <Image
                src="/Images/banner_image.png"
                alt="banner image"
                width={427} // Updated width
                height={363} // Updated height
                className="w-[427px] h-[363px] md:w-[417px] md:h-[339px]"
              />
            </div>
            <p className="text-[#F4F5F6] mx-auto md:ml-7 font-semibold w-full md:text-xl md:leading-8 pl-2 md:border-l-2 md:border-white mt-14 md:font-semibold">
              Launch 3X faster and cheaper with our data driven development
              approach.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeP1;
