import PinkTopCard from "./PinkTopCard";

const Section6 = ({
  PinkTopCardData,
  PinktopCardheight,
  heading,
  spanLeft,
  spanRight,
  paragraph,
}) => {
  return (
    <div className="mx-[20px] mb-10 md:mb-14">
      <div className="mb-[42px]">
        <h2 className="text-xl md:text-3xl text-center font-semibold ">
          <span className="text-[#7716BC]">{spanLeft}</span> {heading}{" "}
          <span className="text-[#7716BC]">{spanRight} </span>
        </h2>
        <p className="text-xl md:w-[70%] mx-auto text-center">{paragraph}</p>
      </div>
      <div className="max-w-fit mx-auto grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-6 md:mt-[0px]">
        {PinkTopCardData.map((card, index) => (
          <PinkTopCard
            key={index}
            title={card.title}
            description={card.description}
            PinkTopCardheight={PinktopCardheight}
          />
        ))}
      </div>
    </div>
  );
};

export default Section6;
