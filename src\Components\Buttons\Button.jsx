"use client";
import React from "react";
import Link from "next/link";

const Button = ({
  children,
  onClick,
  to,
  bgColor = "bg-[#7716BC]",
  hoverColor = "hover:bg-[#5E0D9F]",
  paddingX = "px-2",
  paddingY = "py-1",
}) => {
  const classNames = `text-white text-base md:text-lg font-semibold ${paddingY} ${paddingX} rounded-md transition duration-300 whitespace-nowrap ${bgColor} ${hoverColor}`;

  return to ? (
    <Link href={to} className={classNames}>
      {children}
    </Link>
  ) : (
    <button onClick={onClick} className={classNames}>
      {children}
    </button>
  );
};

export default Button;
