import Image from "next/image";

const InfoCard = ({ title, description }) => {
  return (
    <div className="block w-full md:max-w-xl border h-auto md:h-[230px] border-pink-500  p-2 md:p-4 rounded-md shadow-md">
      <div className="flex items-center gap-2">
        <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8">
          <Image
            src="/Images/service_frame.png"
            alt="Tick"
            width={32}
            height={32}
            className="w-full h-full"
          />
        </div>
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-sm md:text-base text-justify ml-8">{description}</p>
    </div>
  );
};

const Section5 = () => {
  return (
    <section className="bg-blue-100 mb-10 md:mb-24 py-4 md:py-10">
      <div className="w-[85%] mx-auto ">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
          Emerging Trends in {" "}
          <span className="text-[#F245A1]">
            Enterprise Software Development
          </span>
        </h2>
        <p className="w-full md:w-[60%] md:mx-auto text-base md:text-xl text-center">
          The area of enterprise software development is shifting quite rapidly. Here are the trends that are changing industry:  
        </p>
        <div className="flex flex-col justify-center items-center gap-3 md:gap-6 my-6 md:my-[42px] ">
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 md:gap-6">
            <InfoCard
              title={"Low-Code & No-Code Development"}
              description={
                "More and more companies are utilizing low-code and no-code platforms to improve their software development process while minimizing the need for extensive writing. Non-technical personnel can create and edit applications using these very powerful platforms with far more ease than they are used to.  "
              }
            />
            <InfoCard
              title={"AI-Driven Automation"}
              description={
                "AI is changing software applications through automating processes, conducting predictive analytics, and giving customized attention to customers. The development of AI chatbots, intelligent automation systems, and machine learning solutions is improving efficiency in businesses.  "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"Cloud-Native Development "}
              description={
                "Cloud-native applications are the new normal as they allow businesses to create and maintain software systems that can be scaled, are robust, and high-performing. These are certain things that every business wants. This shift is made possible by modern cloud strategies that use microservices architecture and serverless."
              }
            />
            <InfoCard
              title={"Blockchain Integration "}
              description={
                "Blockchain technology improves the security, transparency, and trust of business transactions. Enterprises are adopting blockchain technology for secure sharing of information, supply chain management, and reliable digital identities. "
              }
            />
          </div>
          <div className="flex flex-col md:flex-row justify-center md:justify-between  items-center gap-3 md:gap-6">
            <InfoCard
              title={"Edge Computing"}
              description={
                "With the increase of IoT devices, edge computing is gaining popularity. When data is processed closer to the source, it enhances real-time decision making and security within the connected environment while reducing latency. "
              }
            />
            <InfoCard
              title={"Improvements in Cybersecurity"}
              description={
                "The transformation in frameworks that businesses embrace now is a result of rising cyberattacks. Predictably, enterprise software will incorporate zero trust architecture, AI threat detection, powerful encoders and other forms of security within the devices as the norm. "
              }
            />
          </div>
        </div>
        <div className="w-[80%] mx-auto bg-[#350668] p-6 rounded-md shadow-sm">
          <p className="text-white text-justify text-base md:text-xl">
           Development of Software in a more sustainable and environmentally friendly manner. A company seeks more efficient software, sustainable development and optimized cloud use to lower power consumption and carbon emissions.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Section5;
