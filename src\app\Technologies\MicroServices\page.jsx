import React from "react";
import Section1 from "@/Components/PWA_development/Section1";
import Section2 from "@/Components/PWA_development/Section3";
import Section3 from "@/Components/AI/Section2";
import Section4 from "@/Components/PWA_development/Section5";
import Section5 from "@/Components/MicroServices/Section5";
import Section6 from "@/Components/PWA_development/Section2";
import Section7 from "@/Components/Nlp/Section7";
import Section8 from "@/Components/MicroServices/Section8";
import Faq from "@/Components/Faq/Faq";

const page = () => {
  const Section5cardContent = [
    {
      content: [
        "What distinguishes and enhances other software patterns in a well-designed microservice architecture.",
        "How to create and develop cohesive microservices portfolios so that groups may benefit from one another's efforts without interfering.",
        "How cloud native architecture, DevOps, continuous integration and delivery, and agile governance all work together to support microservices and make them successful.",
        "In order to optimize suitable utilization, particularly for legacy systems, it is important to know when and how to employ microservices.",
        "How to build cross-functional, domain aligned teams around business domains, which are a crucial line of demarcation for fostering independence and cooperation across products and business units.",
      ],
    },
  ];
  const ImageCardData = [
    {
      imgsrc: "/Images/MicroServices4.jpg",
      altsrc: "Microservices Consulting",
      title: "Microservices Consulting",
      description:
        "Divide your company's monolithic system into many separate engines. Our experts offer a well-defined strategy for microservices orchestration, which makes it easier to execute and ensures smooth communication and synchronization across several separate microservices. We can locate and eliminate bottlenecks, carry out updates, and spot improvement possibilities while on the road thanks to our proactive strategy.",
    },
    {
      imgsrc: "/Images/MicroServices5.jpg",
      altsrc: "Microservices Observability",
      title: "Microservices Observability",
      description:
        "To proactively spot problems in a complicated Microservices environment, set up a high-alert system. You can find and fix code-level errors more quickly, obtain past diagnosis reports, and notice problems in real time by increasing observability. We help you make hardware, core infrastructure, and relationships with other systems more observable with technologies like Splunk, Grafana, and New Relic. ",
    },
    {
      imgsrc: "/Images/MicroServices6.jpg",
      altsrc: "Microservices Development",
      title: "Microservices Development",
      description:
        "We manage the creation, testing, and implementation of microservices-based applications on intricate distributed systems as part of our development services. Our proficiency with REST, AMQP, and STOMP allows us to create effective synchronous and asynchronous communication across systems, resulting in a separate yet interconnected collection of microservices that support your business objectives. ",
    },
    {
      imgsrc: "/Images/MicroServices7.jpg",
      altsrc: "Microservices Testing",
      title: "Microservices Testing",
      description:
        "Our development team uses Jenkins to build deployment pipelines that facilitate test automation, continuous integration, and continuous delivery (CI/CD). Our quality assurance engineers check that all of the frontend and business operations procedures function properly and validate middleware and APIs that help with communication. ",
    },
    {
      imgsrc: "/Images/MicroServices8.jpg",
      altsrc: "Microservices Integration",
      title: "Microservices Integration",
      description:
        "We use an API gateway solution for microservices integration, which bridges the frontend and microservices divided by utilizing both microservices and API administration. Instead of having to repeat the process for each microservice separately, this allows for quicker communication, security implementation, and monitoring capabilities at the gateway step.",
    },
    {
      imgsrc: "/Images/MicroServices9.jpg",
      altsrc: "Microservices and DevOps",
      title: "Microservices and DevOps",
      description:
        "We use a DevOps-led Microservices development methodology to enable your apps to take use of Microservices' full potential. We solve problems like distributed monitoring, traffic routing, and service discovery in a complicated microservices system. Our knowledge of Microservices and DevOps also enables you to benefit from quicker releases, decentralization, and scalability.",
    },
  ];
  const Section6cardContent = [
    {
      title:
        "Valueans’ all-around microservices consulting services give your software architecture fundamental agility.  ",
      content: [
        "We make sure your teams are aware of the dos and don'ts when it comes to microservices, including when and how they are beneficial.",
        "The microservices that will propel your business ahead are delivered rapidly and flexibly via our factory architecture.",
        "In addition to the microservices themselves, we define the platforms, tools, and procedures required for the intricate design, configuration, deployment, and production operations of microservices.",
        "Create and instruct your teams on the delivery and governance procedures required to maintain microservices' progress.",
        "Use Git to combine changes and manage versions. ",
        "In addition to legacy renewal techniques and technologies, establish appropriate patterns and rules for the use of containers, microservice frameworks, gateways, micro gateways, service mesh, and other application runtimes.",
        "To train your teams on where and how to use microservices, we hold workshops.",
      ],
    },
  ];
  const pinkBgCardData = [
    {
      title: "Thinking about Domain-Driven Design",
      description:
        "To efficiently map events and data, domain-driven design is a methodology for developing complicated systems into targeted modules that are related to tasks and activities. professionals who can execute the solution (developers/architects) and business professionals who comprehend the process must communicate clearly.",
    },
    {
      title: "Database Sharing",
      description:
        "The next crucial decision is whether or not to share databases. A tight coupling of the microservices is ensured if you want to share the database. The goal of switching to Microservices and its ease of decoupling from the bulk, however, is somewhat negated if you do this. A thorough discussion of this is necessary, and the concept of private tables or distinct databases has to be taken into account.",
    },
    {
      title: "Agile Delivery & Micro frontends",
      description:
        "Microservices should be deployed separately. Agile, DevOps, and CI/CD (Continuous Integration/Continuous Delivery) are all quick development methodologies that are built on agility. In essence, the front end should be broken up in the same way that the back end is. Micro frontends will be awarded for maintaining modularity as needed in the future.",
    },
    {
      title: "Observability and Tracing Are Crucial",
      description:
        "Keeping track of several modules is far more difficult than monitoring a single software system, no matter how big. Because of this, it is crucial to think about how you record, trace, and discover the cause of system delay. There are resources on the market for these jobs that should be thoroughly examined before starting so you are aware of all the expenses related to switching to Microservices. ",
    },
    {
      title: "Use A Proven Language",
      description:
        "Although there are several frameworks, languages, and tools available for implementing microservices, your best option might be to stick with Java, C++, Node.js, .NET (including ASP.NET), Golang (Go), and Python. For instance, you may create your Microservices Architecture using Java and several frameworks, such as Spark, Restlet, and Spring Boot. Another framework that might facilitate the shift is.NET.",
    },
  ];
  const accordionData = [
    {
      title:
        "In microservices, how do several services communicate with one another?",
      content:
        "In order to communicate, services submit messages to a queue, which other services can then read and handle. improves performance and dependability by separating the services and enabling their independent operation.",
    },
    {
      title: "Which is one of the most legitimate issues with microservices?",
      content:
        "The following summarizes the primary issues with microservices architecture: Complex inter-service communications that result in bottlenecks are known as communication delays. Support for data consistency: the challenge of keeping synced data across dispersed systems",
    },
    {
      title:
        "Does the proper operation of microservices necessitate extensive monitoring?",
      content:
        "An essential component of efficient application performance management is ongoing microservice monitoring. Only by monitoring metrics like resource use, latency, and error rates for every microservice will you be able to identify issues early on and be ready to look into them. ",
    },
    {
      title: "Why should every microservice have its data?",
      content:
        "This restriction is in place to prevent inadvertent coupling between services, which may occur when they have similar underlying data structures. Every service that depends on that database must be notified in advance of any changes made to the data schema.",
    },
    {
      title: "What does microservices scalability mean?",
      content:
        "An application's scalability guarantees that it can accommodate expansion in terms of user count, data volume, or transaction rate without sacrificing functionality. Scalability in microservices refers to the capacity to handle additional requests efficiently and economically.",
    },
    {
      title: "What does microservices reliability mean?",
      content:
        "On the other hand, because of the loose coupling of services, microservice architecture may offer increased dependability. Other microservices will continue to function even if one fails. Although throughput may be decreased, catastrophic failure is probably avoided.",
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/MicroServices-bg.jpg"}
        heading={"Microservices consulting at valuenas"}
        bannerText={
          "Leveraging a Reliable, Cost-Effective Architecture with Our Robust Framework"
        }
      />
      <Section2
        image={"/images/MicroServices2.jpg"}
        paragrapgh={
          "We assist you in breaking down monolithic programs, accelerating the adoption of new frameworks and technologies, and preventing single points of failure by using our Microservices knowledge. The Microservices design is very safe and has good API connectivity, even if it is divided into several separate systems."
        }
        heading={"Cloud Based Microservices"}
      />
      <Section3
        lefttext={" Microservice Management"}
        righttext={
          "With machine learning in microservices at Valueans, we ensure a strong understanding of:"
        }
      />
      <Section4
        cardContent={Section5cardContent || []}
        image={"/Images/MicroServices3.jpg"}
      />
      <Section5
        ImageCardData={ImageCardData}
        heading={"Cloud Based Microservices "}
      />
      <Section6
        leftHeading={"Why Choose "}
        blueHeading={"Valueans "}
        rightHeading={"For Microservices"}
        cardContent={Section6cardContent}
      />
      <Section7
        cardData={pinkBgCardData}
        heading={"Key MicroServices "}
        spanHeading={"Best Practices at Valueans"}
      />
      <Section8 />
      <Faq content={accordionData} />
    </div>
  );
};

export default page;
