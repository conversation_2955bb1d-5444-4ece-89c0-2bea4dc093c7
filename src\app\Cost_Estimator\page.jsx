import React from "react";
import Section1 from "@/Components/Cost_Estimator/Section1";
import Section2 from "@/Components/Cost_Estimator/Section2";
import Section4 from "@/Components/Cost_Estimator/Section3";
import Section5 from "@/Components/Cost_Estimator/Section4";
import Section6 from "@/Components/Cost_Estimator/Section5";
import Section3 from "@/Components/Industries/SocialNetwork/Section2";

export const metadata = {
  title: "Software Development Cost Estimator - Get Your Estimate",
  description: "Easily calculate the estimated cost for your custom software development project with Valueans' Cost Estimator.",
};
const page = () => {
  return (
    <div className="bg-[#F7F8FB]">
      <Section1
        heading={"Software Development Cost Estimation"}
        backgroundImage={"/Images/CE-bg.jpg"}
      />
      <Section2
        paragraph1={
          "At Valueans, we offer two ways to get a software development cost estimation for your next project. You can either talk to an expert using our 'Estimate with an Expert' option or get a cost estimation using the 'Use Our Cost Estimator' option."
        }
        paragraph2={
          "Our 'Estimate with an Expert' option allows you to fill out the form with your basic personal information and submit it. Then, our team members will reach out to you, and you can communicate with them."
        }
        paragraph3={
          "Then, another option “Use Our Cost Estimator” allows you to answer 8 questions and submit them. Upon submission, you will instantly get the software development cost estimation for your project. "
        }
        heading={"Our Software Development Cost Estimator"}
        paragraph4={
          "Estimating software development costs for your upcoming projects is a simple process using our “Use Our Cost Estimator” option. All you have to do is answer a series of questions regarding the type of project you wish to develop. Simply choose your answers from the provided options and submit your responses. Once submitted, you immediately get your project’s estimated cost. "
        }
        paragraph5={
          "One solution does not fit everyone, and so the budgeting requires flexibility. With all the information you provide for your software development cost estimation, our software development cost calculator can also offer customization in the estimate for your project. If the estimated cost is above or below your expectations, you can recalculate it by choosing different options for your answers. Here, you can strategically alter your preferences that may significantly impact your project’s cost estimation."
        }
      />
      <Section3
        heading2={"Talk to Our Expert"}
        paragraph2={
          "The project estimate calculator generates a personalized cost estimate once you answer the questions based on your development project requirements. However, if you still need to discuss other factors, we offer you our “Estimate with an Expert” option. It provides you with a customized complete analysis while considering the critical variables like necessary team size, degrees of skill, length of engagement, software type, and complexity. A customized quote that includes the necessary administration panel or optional integrations, as well as development, testing, and deployment schedules, will be provided to you."
        }
        paragraph3={
          "You may discuss variables like priorities or time frames with our expert to see how your changes affect the overall estimate. You are free to discuss your alternatives. Strike a balance between your needs and your budget, and decide on the project scope you want to go with."
        }
        image={"/Images/CE2.jpg"}
      />
      <Section4
        heading={
          "Critical Factors That Affect Estimating Software Development Costs"
        }
        paragraph={
          "Our software development cost estimation calculator undertakes the following aspects to provide you with a cost estimate for your next project."
        }
        card1Title={"Technology Selection"}
        card1Description1={
          "The type of technology used in your software development significantly impacts the total cost of development. You can choose complex technologies, including IoT, Blockchain, etc, or any other less complicated technology for your solution. "
        }
        card1Description2={
          "At Valueans, we have expertise in providing solutions using any of these technologies. However, the expertise and time required for these technologies determine the cost of the project."
        }
        card2Title={"Feature Complexity"}
        card2Description1={
          "Our project estimate calculator also considers what features you want to include in your project. You can choose from basic features to advanced features and check how it impacts the total cost. You can choose whichever feature suits your budget."
        }
        card2Description2={
          "Here, another important aspect is the third-party integrations that might also need to be added to your project. Overall, the software development cost estimation mainly depends on the complexity of the features you choose."
        }
        card3Title={"Team Requirements"}
        card3Description1={
          "Here comes the most important factor that determines the cost of your next project. Now it depends on your needs, how big a team you need for your project. It also depends on what expertise levels are required to develop your desired software solution. "
        }
        card3Description2={
          "As the more expert team charges higher salaries, the cost estimation goes up. However, if we hire junior staff for your project, they can lower the cost, but can only be good for developing less complex features. You can also choose a cross-functional team to find a blend of expert and junior developers."
        }
        card4Title={"Development Timeframe"}
        card4Description1={
          "The software development cost estimation for your next venture also depends a lot on the timeline for your project. If you are in dire need of getting the software in a strict timeline, we need to get more resources for that. And of course, that would result in higher cost for your project."
        }
        card4Description2={
          "Our development calculator estimates the cost by considering how many working hours are required to complete your project. So, you must choose a timeline that suits your budget requirements."
        }
      />
      <Section5
        heading={"Want Us to Develop Your Project?"}
        paragrapgh={"Start Estimating Your Project Cost Now!"}
      />
      <Section3
        heading2={"Valueans Software Development Cost Calculator"}
        paragraph2={
          "Valueans offer a free online project cost calculator for you that helps you estimate the cost of your project in the easiest way possible. We understand it could be frustrating to get quotes for your next project. Valueans makes this process easy for you with its project estimate calculator."
        }
        paragraph3={
          "With our free software development calculator, you can enter all the required information related to your project and get the cost estimation. You can use it multiple times and change your project requirements to get the suitable budget estimation for your project."
        }
        image={"/Images/CE2.jpg"}
        bgColor={"bg-[#794CEC26]"}
          />
          <Section6/>
    </div>
  );
};

export default page;
