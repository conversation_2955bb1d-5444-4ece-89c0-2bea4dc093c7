import Image from "next/image";
import Link from "next/link";
import React from "react";

const PortfolioCard = ({ imageSrc, title, description }) => {
  return (
    <div className="max-w-sm">
      {/* Image Wrapper with Border */}
      <div className="w-[344px] h-[200px] p-2 rounded-lg bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500">
        <div className="relative w-full h-full rounded-lg overflow-hidden">
          <Image
            src={imageSrc}
            alt={title}
            layout="fill" // Fills the parent container
            objectFit="cover" // Ensures the image covers the container
            className="rounded-lg" // Match the border radius of the wrapper
          />
        </div>
      </div>

      {/* Content */}
      <div className="p-1">
        <Link href="#"> 
          <h5 className="mb-2 text-lg md:text-2xl font-bold tracking-tight text-center">
            {title}
          </h5>
        </Link>
        <p className="mb-2 text-sm md:text-base font-light text-justify">
          {description}
        </p>
      </div>
    </div>
  );
};

export default PortfolioCard;
