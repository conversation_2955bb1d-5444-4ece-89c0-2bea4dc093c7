import React from "react";

const Card = ({ title, content, className, height }) => {
  return (
    <div
      className={`block w-full md:max-w-[300px] ${height? height : "h-[200px]" }  p-6 md:pb-10 bg-white border border-purple-500 rounded-lg shadow-lg overflow-hidden  ${className}`}
    >
      <h5 className=" mb-2 text-base md:text-lg font-bold tracking-tight">
        {title}
      </h5>
      <div className="font-normal px-2 text-sm md:text-base">
        {Array.isArray(content) ? (
          <ul style={{ listStyleType: "disc" }}>
            {content.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        ) : (
          <p>{content}</p>
        )}
      </div>
    </div>
  );
};

export default Card;
