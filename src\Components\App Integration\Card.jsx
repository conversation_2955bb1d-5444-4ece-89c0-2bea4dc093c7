import React from "react";

const Card = ({ title, description, height, border, width, headingFont }) => {
  return (
    <>
      <div
        className={`block ${width ? width : "w-full"}  md:max-w-xl ${height ? height : ""} p-6 bg-white border ${border ? border : "border-gray-200"}  rounded-lg shadow overflow-hidden`}
      >
        <h5
          className={` mb-2  ${headingFont ? headingFont : "text-lg md:text-2xl text-[#7716BC]"}  font-bold tracking-tight `}
        >
          {title}
        </h5>
        <p className="font-normal text-sm md:text-base text-justify " dangerouslySetInnerHTML={{
            __html:  description,
          }}>
        </p>
      </div>
    </>
  );
};

export default Card;
