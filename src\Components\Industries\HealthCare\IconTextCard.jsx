import Image from "next/image";
import React from "react";

const IconTextCard = ({ cardData }) => {
  return (
    <div className="flex flex-wrap gap-4">
      {cardData.map((card, index) => (
        <div
          key={index}
          className=" bg-white border p-2  rounded-lg w-[100%] border-[#7716BC] "
        >
          <div className="flex items-center gap-3">
           {card.image && <Image src={card.image} alt={card.alt} width={32} height={32} />}
            <h3 className="text-base md:text-lg font-semibold">{card.heading}</h3>
          </div>
          <p className="text-sm">{card.paragraph}</p>
        </div>
      ))}
    </div>
  );
};

export default IconTextCard;
