import React from "react";
import ServiceCount from "../Services/ServiceCount_2";
import ServiceLifecycleCard from "../Services/ServiceLifecycleCard_2";

const Process = () => {
  const requirementGatheringAndAnalysis = [
    "Consultations with stakeholders to define business objectives.",
    "Identification of scalability, security, and performance needs.",
    "Feasibility analysis and decision on cloud platforms (AWS, Google Cloud, Azure, etc.).",
  ];

  const cloudArchitectureDesign = [
    "Designing scalable, secure, and cost-effective cloud architectures.",
    "Selection of service models (IaaS, PaaS, SaaS) based on client needs.",
    "Define security protocols, disaster recovery, and high availability strategies.",
  ];

  const developmentAndImplementation = [
    "Coding and development of applications/services using cloud-native tools.",
    "Integration with third-party services or on-premise systems if necessary.",
    "Developing microservices architecture, if applicable, to ensure modularity and scalability.",
    "Implement containerization (using Docker, Kubernetes) to ensure consistent environments across development and production.",
  ];

  const testingAndQualityAssurance = [
    "Functional, performance, and security testing.",
    "Automated testing pipelines for continuous integration (CI) and continuous delivery (CD).",
    "Load testing to check system performance under high traffic.",
  ];

  const deploymentAndCloudMigration = [
    "Implement DevOps practices for smooth deployment and integration.",
    "Automate deployment using tools like Jenkins, AWS CodePipeline, or Azure DevOps.",
    "Migrate data and legacy systems to the cloud (if applicable).",
  ];

  const monitoringAndOptimization = [
    "Install monitoring software (such as CloudWatch or Azure Monitor) to monitor uptime and performance.",
    "Optimize for cost and scalability by fine-tuning resource allocation.",
    "Automated backups and disaster recovery implementations.",
  ];

  const supportAndMaintenance = [
    "Regular updates and patching of cloud services.",
    "Continuous improvement cycles based on performance analytics.",
    "Technical support and troubleshooting.",
  ];

  const scalingAndFutureEnhancements = [
    "Implement scalable solutions to accommodate traffic and data growth.",
    "Develop and deploy additional features as required.",
    "Optimize and modernize infrastructure with emerging technologies (e.g., AI/ML, IoT).",
  ];

  return (
    <div className="bg-blue-100 py-10 my-10 md:my-24">
      <div className="w-[90%] md:w-[85%] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-bold text-[#F245A1]">
         Our Cloud Development <span className="text-[#232536]">Process:</span>
        </h2>
        <p className="text-base md:text-xl text-center">
          The development process of cloud development services by Valueans can
          be broken <br /> down into several key phases to ensure a structured
          and efficient approach. 
        </p>
        <div className="my-5 md:my-10">
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>1</ServiceCount>
              <ServiceLifecycleCard
                title="Requirement Gathering and Analysis"
                items={requirementGatheringAndAnalysis}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>2</ServiceCount>
              <ServiceLifecycleCard
                title="Cloud Architecture Design"
                items={cloudArchitectureDesign}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>3</ServiceCount>
              <ServiceLifecycleCard
                title="Development and Implementation"
                items={developmentAndImplementation}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>4</ServiceCount>
              <ServiceLifecycleCard
                title="Testing and Quality Assurance"
                items={testingAndQualityAssurance}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>5</ServiceCount>
              <ServiceLifecycleCard
                title="Deployment and Cloud Migration"
                items={deploymentAndCloudMigration}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>6</ServiceCount>
              <ServiceLifecycleCard
                title="Monitoring and Optimization "
                items={monitoringAndOptimization}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>7</ServiceCount>
              <ServiceLifecycleCard
                title="Support and Maintenance"
                items={supportAndMaintenance}
              />
            </div>
            <div className="flex justify-center items-center gap-2 my-5">
              <ServiceCount>8</ServiceCount>
              <ServiceLifecycleCard
                title="Scaling and Future Enhancements"
                items={scalingAndFutureEnhancements}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Process;
