import React from "react";

const Container = ({ children, bgColor = "bg-white", className = "" }) => {
  return (
    <div className={`w-full p-3 md:px-[42px] md:py-6 ${bgColor} ${className}`}>
      {children}
    </div>
  );
};
const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block max-w-sm p-3 md:h-[200px] md:p-4 shadow-sm rounded-md ${bgColor}`}
    >
      <h3 className="text-lg md:text-xl font-semibold capitalize">
        {title}
      </h3>
      <p className="text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section7 = () => {
  return (
    <div className="w-[95%] mx-auto mb-10 md:mb-24">
      <h2 className="text-xl md:text-3xl text-center font-semibold">
        You might ask why <span className="text-[#7716BC]">Valueans?</span>{" "}
      </h2>
      <div className="w-full md:w-[85%] md:mx-auto mt-6 md:mt-[42px]">
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-200"
            title={"Domain Knowledge:"}
            description={
              " Our developers have experience building custom software to fit the precise needs of varying industries, ranging from healthcare and social networking solutions to creating software for gaming sector, automotive, and insurance companies "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Agile Development:"}
            description={
              "Our clients’ feedback is the bedrock of our refining process which leads to a proactive superior product. Our infrastructure is designed to prioritize on time delivery, flexibility, and client care. "
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center  gap-3 p-3 md:p-6 my-3 md:my-6"
          bgColor="bg-violet-100"
        >
          <Card
            bgColor="bg-violet-200"
            title={"Triangular Integration:"}
            description={
              "User usability is crucial when it comes to software that pays, changes, and upgrades. We provide custom solutions for whether any API set up or system changes are needed, as trouble free setups can be done wherever necessary.  "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Evolving & Safe Products:"}
            description={
              "We gear the medium to advanced protection program for our clients alongside plans for the coming future while ensuring their information is fully safeguarded.  "
            }
          />
        </Container>
        <Container
          className="flex flex-col md:flex-row justify-center md:justify-between items-center  gap-3 p-3 md:p-6"
          bgColor="bg-pink-100"
        >
          <Card
            bgColor="bg-purple-100"
            title={"Measurable And Cost Effective:"}
            description={
              "We develop top of the line software to deliver to our clients, making sure the profits exceed the costs greatly so that all bases are covered. "
            }
          />
          <Card
            bgColor="bg-pink-200"
            title={"Absolute Support:"}
            description={
              "Our maintenance, software updates, and troubleshooting services fortify our client supporting after a project."
            }
          />
        </Container>
      </div>
    </div>
  );
};

export default Section7;
