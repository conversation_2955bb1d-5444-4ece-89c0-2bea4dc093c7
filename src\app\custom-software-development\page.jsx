import Faq from "@/Components/Faq/Faq";
import HomeP8 from "@/Components/Homepage/HomeP8";
import Banner from "@/Components/Services/Banner";
import FeaturedProject from "@/Components/Services/FeaturedProject";
import ProcessFlow from "@/Components/Services/ProcessFlow";
import ServiceCount from "@/Components/Services/ServiceCount";
import ServiceDescription from "@/Components/Services/ServiceDescription";
import ServiceDropdown from "@/Components/Services/ServiceDropdown";
import ServicePage2 from "@/Components/Services/ServicePage2";
import ServicePage3 from "@/Components/Services/ServicePage3";
import ServiceTest from "@/Components/Services/ServiceTest";

import ServiceTestlife from "@/Components/Services/ServiceTestlife";

import React from "react";

const Custom_Software_Development = () => {
  return (
    <>
      <Banner
        title=" that meet your Unique Requirements"
        titleHighlight="Custom Software Development Services"
        description="Innovate, transform, and lead with"
        descriptionHighlight="Valueans"
        Imagesrc={"/Images/custom_soft_1.png"}
        ImageAlt={"custom software development"}
        highlightColor="#F245A1"
      />
      <ServiceDescription
        primaryText={
          "Steer your business direction with Valueans, where we prioritize new ideas and creativity to help you lead in the market. We combine automated and creative solutions that uplift your business and provide dynamic products and "
        }
        primaryTextHighlight={"advanced custom software development services."}
        secondaryText={
          "From ideation to deployment and designing to testing, we specialize in everything."
        }
      />
      <ServicePage2 />
      <ServicePage3 />
      <ServiceTest />
      {/* <ServiceDropdown /> */}
      {/* <ServiceLifecycle /> */}
      <ServiceTestlife />
      <FeaturedProject />
      <HomeP8 />
      <Faq />
      {/* <ServiceLifecycle /> */}
    </>
  );
};

export default Custom_Software_Development;
