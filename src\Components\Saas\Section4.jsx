import React from "react";

const Card = ({ title, description, bgColor = "bg-white" }) => {
  return (
    <div
      className={`block w-full md:w-[260px] h-auto md:h-[320px] p-4 ${bgColor} border border-gray-200 rounded-lg shadow-md`}
    >
      <h5 className="text-[#7716BC] text-base md:text-xl font-bold tracking-tight mb-1  md:mb-3">
        {title}
      </h5>
      <p className="font-normal text-sm md:text-base text-justify">{description}</p>
    </div>
  );
};

const Section4 = () => {
  return (
    <div className="w-[85%] mx-auto mb-10 md:mb-24">
  <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
    Saas Product Development  Services by{" "}
    <span className="text-[#F245A1]">Valueans</span>
  </h2>

  <div className="my-3 md:my-6">
    {/* First row: Grid with 4 columns on md+ screens, single column on mobile */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-3 md:gap-6 mb-3 md:mb-6">
      <Card
        title={"SaaS UI/UX"}
        description={
          "Thoroughly studying the market and working out what this space needs moving forward. Creating wireframes and prototypes to lay out the foundational structure. Finally running iterations post user testing to iron out any inconsistencies."
        }
      />
      <Card
        title={"SaaS API Development"}
        description={
          "Bridge the gap by creating APIs that allow third party apps to access your platform and communicate under a set standard/protocol."
        }
        bgColor="bg-blue-100"
      />
      <Card
        title={"Migration Services"}
        description={
          "Moving your systems to another environment with little to no disruptions. No hassle while handling different types of migration for example data migration, application migration, cloud migration, and platform migration."
        }
      />
      <Card
        title={"Saas MVP Development"}
        description={
          "In order to garner early attention and get the users attracted we create an MVP for the product/application of your choice. Acting on feedback and fine tuning the apps before full launch is always a wise decision."
        }
        bgColor="bg-blue-100"
      />
    </div>

    {/* Second row: Flex with 3 cards, each on its own row in mobile */}
    <div className="flex flex-col md:flex-row justify-center items-center gap-3 md:gap-6">
      <Card
        title={"Saas Growth Hacking"}
        description={
          "Starting a company from scratch is easier said than done. We help businesses grow quickly by using as few resources as possible staying within budget. It's like having a cheat sheet of sorts."
        }
      />
      <Card
        title={"White Label Solutions"}
        description={
          "You can always purchase a ready-to-go solution and market it as your own. Enter the market faster and save your time. Let us do the heavy lifting."
        }
        bgColor="bg-blue-100"
      />
      <Card
        title={"Saas Monthly Performance Benchmarks"}
        description={
          "Worried how your app will turn out post release? Well don't, because we will be checking up on the apps/products and their functionality to ensure it runs as a well oiled machine."
        }
      />
    </div>
  </div>
</div>

  );
};

export default Section4;
