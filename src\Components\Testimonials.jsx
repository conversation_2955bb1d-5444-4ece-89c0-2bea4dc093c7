"use client";
import { useState, useEffect } from "react";
import { useTransition, animated } from "react-spring";
import TestimonialCard from "./Card/TestimonialCard";
import Heading from "./Heading/Heading";

const dummyReviews = [
  {
    review:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. Velit officia consequat duis enim velit mollit. Exercitation veniam consequat ",
    name: "<PERSON>",
    imageSrc: "/Images/testimonial.png",
    title: "Lead solution designer",
  },
  {
    review:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. Velit officia consequat duis enim velit mollit. Exercitation veniam consequat ",
    name: "<PERSON>",
    imageSrc: "/Images/testimonial.png",
    title: "CEO",
  },
  {
    review:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. Velit officia consequat duis enim velit mollit. Exercitation veniam consequat ",
    name: "<PERSON>",
    imageSrc: "/Images/testimonial.png",
    title: "Lead solution designer",
  },
];

function TestimonialCarousel() {
  const [activeStep, setActiveStep] = useState(0);
  const [reviews, setReviews] = useState(dummyReviews);

  useEffect(() => {
    const intervalId = setInterval(() => {
      handleNext();
    }, 5000);

    return () => clearInterval(intervalId);
  }, [activeStep, reviews.length]);

  const handleNext = () => {
    setActiveStep((prev) => (prev < reviews.length - 1 ? prev + 1 : 0));
  };

  const handleBack = () => {
    setActiveStep((prev) => (prev > 0 ? prev - 1 : reviews.length - 1));
  };

  const transitions = useTransition(activeStep, {
    from: { opacity: 0, transform: "translateX(-100%)" },
    enter: { opacity: 1, transform: "translateX(0%)" },
    leave: { opacity: 0, transform: "translateX(100%)", display: "none" },
    config: { duration: 500 },
  });

  return (
    <section className="bg-pink-100 testimonial-section p-6 md:p-8 my-12 md:my-24">
      <div className="flex flex-col md:flex-row max-w-[95%] md:max-w-[80%] mx-auto my-6 md:my-8">
        <div className="w-full md:w-1/3 p-4 md:mr-16">
        <Heading>
        What our clients say about us
        </Heading>
      
         
          <div className="flex justify-center items-center mt-4">
            <button
              onClick={handleBack}
              className={`p-2 md:p-4 rounded-full mr-2 md:mr-4 flex items-center justify-center ${
                activeStep === 0
                  ? "bg-white text-gray-500 cursor-not-allowed"
                  : "bg-pink-500 hover:bg-pink-600 text-white"
              }`}
              disabled={activeStep === 0}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 md:h-6 w-5 md:w-6 ${
                  activeStep === 0 ? "text-gray-500" : "text-white"
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <button
              onClick={handleNext}
              className={`p-2 md:p-4 rounded-full flex items-center justify-center ${
                activeStep === reviews.length - 1
                  ? "bg-white text-gray-500 cursor-not-allowed"
                  : "bg-pink-500 hover:bg-pink-600 text-white"
              }`}
              disabled={activeStep === reviews.length - 1}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-5 md:h-6 w-5 md:w-6 ${
                  activeStep === reviews.length - 1
                    ? "text-gray-500"
                    : "text-white"
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="w-full md:w-2/3 mt-8 md:mt-0">
          <div className="w-full">
            {transitions((styles, item) => (
              <animated.div
                key={item}
                style={{ ...styles, width: "100%" }}
                className="flex justify-center"
              >
                <TestimonialCard
                  review={reviews[item]?.review}
                  name={reviews[item]?.name}
                  title={reviews[item]?.title}
                  imageSrc={reviews[item]?.imageSrc}
                />
              </animated.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default TestimonialCarousel;
