import React from "react";

const PinkTopWithBullets = ({ title, bulletPoints, PinkTopCardheight }) => {
  return (
    <div className="max-w-md relative ">
      <div className="bg-pink-100 p-4 h-[100px] rounded-t-md absolute top-[-20px] w-full z-10 shadow-md text-center flex items-center justify-center">
        <h3 className="text-base md:text-lg font-semibold">{title}</h3>
      </div>

      <div
        className={`w-full border border-purple-500 p-1 rounded-md shadow-sm pt-16 h-auto ${PinkTopCardheight ? PinkTopCardheight : ""}`}
      >
        <ul className="text-sm md:text-base m-5 text-justify px-3 space-y-2">
          {/* Replace each description item with a bullet point */}
          {bulletPoints.map((point, idx) => (
            <li key={idx} className="list-disc ">
              {point}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PinkTopWithBullets;
