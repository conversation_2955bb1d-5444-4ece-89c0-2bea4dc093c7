import ServiceCount from "../Services/ServiceCount_2";

const Card = ({ title, description }) => {
  return (
    <div className="block max-w-xl md:h-[150px] h-auto p-4 border border-purple-600 bg-white shadow-sm rounded-md">
      <h3 className="text-base font-semibold">{title}</h3>
      <p className="text-sm mt-1 text-justify">{description}</p>
    </div>
  );
};

const Section6 = () => {
  return (
    <section className="bg-pink-100 mb-10 md:mb-24 py-3 md:py-8">
      <div className="w-[85vw] mx-auto">
        <h2 className="text-xl md:text-3xl md:leading-8 text-center font-semibold mb-1 md:mb-2">
          Comprehensive{" "}
          <span className="text-[#F245A1]">
            Software Development Lifecycle (SDLC) 
          </span>{" "}
          Management
        </h2>
        <p className="md:w-[75%] md:mx-auto text-base md:text-xl text-center">
         At Valueans we have set up an admirable Software Development Lifecycle (SDLC) that guarantees quality, efficiency, and reliability in every project. The value chain of this process encompasses the following:
        </p>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4 mt-8">
          <div className="flex flex-col justify-center items-center gap-4">
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>1</ServiceCount>
              <Card
                title={"Requirement Analysis"}
                description={
                  "At this stage we begin by understanding the business needs alongside conducting requirement documentation and feasibility studies which set clear aims for the project."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              {" "}
              <ServiceCount>3</ServiceCount>
              <Card
                title={"Development"}
                description={
                  "All solutions are designed by our developers using agile techniques, building fingers to hand coordination that allows smooth operational system integration and performance with existing solutions."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>5</ServiceCount>
              <Card
                title={"Deployment & Integration"}
                description={
                  "The application is deployed on ‘live’ servers with preset limits within which business operations can begin making the switch seamless."
                }
              />
            </div>
          </div>
          <div className="flex flex-col justify-center items-center gap-4">
            <div className="flex justify-center items-center gap-2">
              {" "}
              <ServiceCount>2</ServiceCount>
              <Card
                title={"Planning & Design"}
                description={
                  "Our specialists design software architecture, systems user interface and user experience and issue the specification documents which are aimed at making development and use easier."
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>4</ServiceCount>
              <Card
                title={"Testing & Quality Assurance"}
                description={
                  "Ultrasound functional and performance tests, as well as usability and security tests of the product create a reliable defect free software application. "
                }
              />
            </div>
            <div className="flex justify-center items-center gap-2">
              <ServiceCount>6</ServiceCount>
              <Card
                title={"Maintenance & Support"}
                description={
                  "Support after launch is assured through the maintenance, security, optimization and enhancement of the application."
                }
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Section6;
