import React from "react";
import Image from "next/image";
import Section1 from "@/Components/Industries/HealthCare/Section1";
import Section2 from "@/Components/Industries/HealthCare/Section2";
import Section3 from "@/Components/Industries/HealthCare/Section3";
import Section4 from "@/Components/Industries/HealthCare/Section4";
import Section5 from "@/Components/Industries/HealthCare/Section5";
import Section6 from "@/Components/Industries/HealthCare/Section6";
import Section7 from "@/Components/Industries/HealthCare/Section7";
import Faq from "@/Components/Faq/Faq";

const page = () => {
  const PinkDotCardData = [
    {
      content: [
        "Minimizing Medical Data Errors",
        "Enhancing Data Security",
        "Reducing Costs",
        "Improving Patient-Provider Communication",
        "Ensuring Regulatory Compliance",
      ],
    },
  ];
  const PinkDotCardData2 = [
    {
      title: "Sophisticated Features Tailored to Your Requirements",
      feature:
        "We can create software of any complexity and customize its functionality to meet the needs of various medical specializations thanks to our proficiency in cutting-edge technologies.",
    },
    {
      title: "Quicker Software Delivery ",
      feature:
        "Our Agile methodology, well-established CI/CD and DevOps procedures, and a useful amount of testing automation allow us to guarantee the quick delivery of MVPs and regular software updates.",
    },

    {
      title: "Unquestionable Compliance and Security",
      feature:
        "We follow OWASP guidelines to provide reliable and secure software. Complete PHI protection and compliance at every stage are ensured by our team of cybersecurity specialists and healthcare regulatory advisors.",
    },

    {
      title: "Smooth Interaction With Other Devices and Software",
      feature:
        "We integrate software to medical equipment via the latest protocols to make sure it is easy to use. We use standards to integrate medical software and enable electronic data interchange (EDI).",
    },
  ];
  const cardData = [
    {
      title: "IT Solutions for Healthcare Industry Services",
      content:
        "You may get assistance from our healthcare IT specialists with feature mapping, software technical design, and regulatory compliance. Additionally, we are ready to design and improve a development project, establish a marketing or adoption strategy, evaluate software costs and ROI, and educate your internal staff.",
    },
    {
      title: "Consulting & Strategy Development",
      content:
        "Our technology consultants collaborate closely with your team to gain a comprehensive understanding of the current infrastructure, workflows, and strategic goals and to offer you the most suitable software development in healthcare. We carry out thorough evaluations to identify potential for innovation and efficiency enhancements. After this, we create a meticulous plan for implementing and integrating technology.",
    },
    {
      title: "AI-powered Healthcare Solutions",
      content:
        "Our company creates tailored AI-driven healthcare solutions that revolutionize processes and workflows in the healthcare industry, leading to better decision-making and enhanced patient interactions. Our solutions improve diagnostic precision, tailor treatment plans to individuals  , and increase the overall effectiveness of healthcare services. We guarantee a comprehensive enhancement of patient care as well as operational effectiveness.",
    },
    {
      title: "Healthcare Software Product Development",
      content:
        "We possess the know-how to successfully introduce an app, owing to our experience in developing software products. Our specialists will conduct a market analysis and develop your software concept into a product, defining its distinct brand identity, features, and architecture. You receive a medical software product that is fully compliant with HIPAA, GDPR, and other regulations.",
    },
    {
      title: "Custom Healthcare Software Development",
      content:
        "Our team includes an MD consultant, allowing us to conduct a comprehensive analysis of your workflows and requirements. We then create a customized solution that integrates seamlessly with your current IT ecosystem. We guarantee the security of PHI and ensure that our software complies with HIPAA, GDPR, and other applicable regulations.",
    },
    {
      title: "Healthcare Software Evolution",
      content:
        "The staff at Valueans is prepared to tackle the challenge of adding new features to your app or completely revamping your outdated software. From evolution planning and the implementation of upgraded software to reverse engineering, we are ready to handle any facet.",
    },
  ];
  const infoCardData = [
    {
      image: "/Images/HCicon1.png", // Replace with your image URL
      heading: "Implement AI throughout the company",
      paragraph:
        "Valueans boost efficiency in key workflows and help your healthcare organization accelerate the effect of generative AI.​",
    },
    {
      image: "/Images/HCicon2.png", // Replace with your image URL
      heading: "Utilize cloud solutions to maximize technology",
      paragraph:
        "Utilize HIPAA-enabled and GxP-enabled data sets to inform improved point-of-care choices with cloud solutions for the healthcare ecosystem.",
    },
    {
      image: "/Images/HCicon3.png", // Replace with your image URL
      heading: "Make more intelligent consumer experiences",
      paragraph:
        "Utilize the technology, data, and tools that enable excellent customer service. Obtain assistance with process improvement with comprehensive IT services and solutions.",
    },
    {
      image: "/Images/HCicon4.png", // Replace with your image URL
      heading: "Boost output while controlling expenses",
      paragraph:
        "Build the future of healthcare and ensure application and infrastructure performance to provide dynamic business outcomes.",
    },
    {
      image: "/Images/HCicon5.png", // Replace with your image URL
      heading: "Store patient information and medical records efficiently",
      paragraph:
        "For quick and accurate decision-making, easily access healthcare data more quickly, intelligently, and economically.",
    },
    {
      image: "/Images/HCicon6.png", // Replace with your image URL
      heading: "Boost the security of data",
      paragraph:
        "Safeguard patient information, company operations, and healthcare data on devices that are part of your network and that healthcare providers utilize. ",
    },
  ];

  const PinkTopCardData = [
  {
    title: "The Basis of Care",
    description:
      "Valueans recognizes the value of a well-tuned, secure, and scalable infrastructure. Digital infrastructure, data centers, IT operations, and security are all covered in our Foundations of Care for Healthcare technology strategy and solutions.",
  },
  {
    title: "Enhancing Care",
    description:
      "The emphasis may now be on enhancing the people, procedures, and technology that can aid maximize care once the foundation is strong and secure. With an emphasis on data and analytics as well as application optimization, our healthcare technical professionals assist you in optimizing the use of your current IT assets.",
  },
  {
    title: "The Future of Healthcare",
    description:
      "Not only do we assist in fortifying the foundation and optimizing technology to support care in the present, but we also build solutions for the future that prioritize improving treatment outcomes, increasing organizational efficiency, and enhancing clinician and patient satisfaction.",
  },
  {
    title: "'Next' Patient Room",
    description:
      "In order to advance conventional in-patient rooms, exam rooms, operation rooms, at-home healthcare solutions, and neighborhood clinics, Valueans' integrated solution, Patient Room",
  },
  {
    title: "Utilize cloud solutions to maximize technology",
    description:
      "'Next,' reinterprets care models and procedures. This provides providers with automatic documentation and real-time insights from data.",
  },
  { 
    title: "Senior and Post-Acute Care",        
    description:
      "Technology is more important than ever as the U.S. population ages and the business struggles with a scarcity of clinicians. The issues faced by independent living facilities, assisted living facilities, skilled nursing facilities, and other elder care providers are comparable. You must make sure that the experiences of your staff, residents, and patients are unmatched.",
  },
  ];
  const accordionData = [
    {
      title: "Which technologies are accessible in the field of healthcare?",
      content: "A range of medical technologies that are widely accepted by the community are utilized by professionals. This encompasses machine tools like X-ray machines and MRIs, as well as innovations aimed at enhancing public health, including vaccines and health wearables.",
    },
    {
      title: "Who develops medical software?",
      content: "Valueans stands out as a leading figure in the development of healthcare software. Our specialization lies in tailored healthcare tech solutions, which include healthcare IT services like electronic health record systems, medical analytics, and telemedicine application development.",
    },
    {
      title: "What is the cost of developing medical software?",
      content: "The expenses incurred in developing a healthcare app depend on its complexity and features. The development of a prescription app that requires database connectivity can cost as much as $45,000, whereas a telemedicine app needing extensive security and compliance measures can reach costs of up to $150,000. ",
    },
    {
      title: "What steps are involved in the creation of healthcare software?",
      content: "There are several steps in the development of healthcare software. It includes designing, developing, testing, and maintaining healthcare tech solutions, especially for patients, healthcare providers, and other stakeholders is known as software development in healthcare. The process of creating the ideal healthcare app is time-consuming and complex. Not all developers may be proficient at it.",
    },
    {
      title: "Will the code for the medical software belong to us?",
      content: "Of course! We enter into client services agreements that specify that our clients must receive the healthcare software we create, including the code.",
    },
    {
      title: "How much time does it take to get the healthcare project started?",
      content: "Following your free consultation, Valueans will have a committed group of professionals who can begin working on your healthcare software solutions project as soon as two weeks.",
    },
  ];
  return (
    <div>
      <Section1
        backgroundImage={"/Images/HealthCare-bg1.jpeg"}
        heading={"Software Development in healthcare"}
        bannerText={
          "Delivering reliable medical software products and custom solutions"
        }
      />
      <Section2
        heading={"Professional Development in Healthcare"}
        paragraph={
          "Valueans develops public health and technology software to design secure medical applications for care delivery and process optimization that comply with most reputable healthcare standards. We develop software solutions that meet regulatory compliance and security standards, facilitating communication in the healthcare sector."
        }
      />
      <Section3
        PinkDotCardData={PinkDotCardData}
        heading={"Valueans Software Development Healthcare Industry"}
        image={"/Images/HealthCare2.png"}
        paragraph={
          "With our services in software development in healthcare, we help you in:"
        }
      />
      <Section4 cardData={cardData} />
      <Section5
        PinkDotCardData={PinkDotCardData2}
        headingLeft={"Why Choose"}
        spanHeading={"Valueans"}
        headingRight={"For Software Development in HealthCare"}
        image={"/Images/HealthCare3.jpeg"}
      />
      <Section6 cardData={infoCardData} headingLeft={"Valueans Solution For"} headingRight={"Technology"} spanHeading={"Healthcare"} image={"/Images/HealthCare4.png"} />
      
      <div className="my-10 md:my20">
      <Image src="/Images/HealthCare5.png" alt="HealthCare" width={1440} height={300}/>
      </div>
      <Section7 PinkTopCardData={PinkTopCardData} Cardheight={"md:h-[360px]"} heading={"Valueans Software Development Healthcare Industry"} />
      <Faq content={accordionData}/>
    </div>
  );
};

export default page;
