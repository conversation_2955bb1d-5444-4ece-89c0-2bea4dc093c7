import React from "react";
import Tab from "@/Components/Portfolio/Tab";
import UI_UX from "@/Components/Portfolio/UI-UX";

const UIUXPortfolio = () => {
  return (
    <section className="bg-white">
      <div className="max-w-[85vw] mx-auto py-12">
        <div className="w-[70vw] mx-auto">
          <h2 className="text-4xl text-center text-[#F245A1] font-semibold">
            UI/UX Design Portfolio
          </h2>
          <p className="text-center text-lg font-normal mt-10">
            Explore our collection of user-centered design projects that combine
            aesthetics with functionality. Our UI/UX designs focus on creating
            intuitive, engaging, and accessible digital experiences that delight
            users and drive business results.
          </p>
        </div>
        <div className="max-w-fit mx-auto my-14">
          <Tab />
        </div>
      </div>
      <UI_UX />
    </section>
  );
};

export default UIUXPortfolio;
