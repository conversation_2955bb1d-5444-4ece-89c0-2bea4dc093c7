import Image from "next/image";
import React from "react";

const TestimonialCard = ({name,review,imageSrc,title}) => {
  return (
    <div className="block max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 ">
      <div className="flex items-center space-x-4">
        <div className="w-[90px] h-[90px] rounded-full overflow-hidden">
          <Image
              src={imageSrc}
              alt={name}
            width={90}
            height={90}
            className="rounded-full object-cover" // Add rounded-full to the Image
          />
        </div>
        <div>
          <h5 className="mb-2 text-lg font-bold tracking-tight ">
            {name}
          </h5>
          <h6 className="text-xs">{title}</h6>
        </div>
      </div>

      <p className="font-normal ">
       {review}
      </p>
    </div>
  );
};

export default TestimonialCard;
