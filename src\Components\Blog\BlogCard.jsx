import React from "react";
import Image from "next/image";
import Link from "next/link";

const BlogCard = ({ title, description, date, imageUrl }) => {
  return (
    <Link href="/blogpage" passHref>
      {" "}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden w-80 p-6">
        <div className="relative max-w-[330px] max-h-[241px]">
          <Image
            src={imageUrl}
            alt={title}
            layout="responsive"
            width={330}
            height={241}
            objectFit="cover"
          />
        </div>
        {/* Content Section */}
        <div className="p-4 mt-2">
          <h2 className="text-lg font-semibold text-gray-800 ">{title}</h2>
          <p className="text-gray-600 mt-3 mb-1 text-base">{description}</p>
        </div>
        {/* Footer Section */}
        <div className="px-4 py-2 flex items-center justify-between border-t border-gray-200">
          <span className="text-sm text-[#232536] font-light">{date}</span>
          <a
            href="#"
            className="text-[#232536] hover:text-blue-600 text-base font-medium"
          >
            Read More &rarr;
          </a>
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
