"use client";
import Image from "next/image";
import React from "react";
import Link from "next/link";

const Technologies = () => {
  return (
    <div className="md:container md:mx-auto md:p-10 md:border md:rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Image Section */}
        <div className="col-span-1 md:col-span-2">
          <Image className="hidden md:block" src={"/Images/tech-nav.jpeg"} width={400} height={400} />
        </div>

        {/* Links Section */}
        <div className="col-span-1 md:col-span-3 space-y-4 md:space-y-0 md:flex md:justify-between">
          {/* Column 1 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Technologies/Progressive_Web_Apps" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Progressive Web Apps
            </Link>
            <Link href="/Technologies/NLP" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              NLP
            </Link>
            <Link href="/Technologies/Low_Code_Development" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Low Code Deployment
            </Link>
          </div>

          {/* Column 2 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Technologies/AR_VR" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              AR/VR
            </Link>
            <Link href="/Technologies/MicroServices" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Microservices
            </Link>
            <Link href="/Technologies/Predictive_Analysis" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Predictive Analytics
            </Link>
          </div>

          {/* Column 3 */}
          <div className="flex flex-col gap-3 md:w-[30%]">
            <Link href="/Technologies/IOT" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              IoT
            </Link>
            <Link href="/Technologies/AI_ML" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              AI/ML
            </Link>
            <Link href="/Technologies/Cross_Platform_And_Hybrid_Development" className="text-sm md:text-base text-gray-900 hover:text-[#F245A1] hover:underline">
              Cross-platform and Hybrid Development
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Technologies;
