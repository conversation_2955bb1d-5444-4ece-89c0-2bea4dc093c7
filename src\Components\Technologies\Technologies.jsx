"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";

const technologies = [
  { name: "Predictive analytics", image: "/Images/data-analysis.png" },
  { name: "Progressive web apps", image: "/Images/app-development.png" },
  { name: "Low code deployment", image: "/Images/coding.png" },
  { name: "NLP", image: "/Images/natural-language-processing.png" },
  { name: "AR & VR", image: "/Images/virtual-reality.png" },
  { name: "Microservices", image: "/Images/microservice.png" },
  { name: "IOT", image: "/Images/iot.png" },
  { name: "Saas", image: "/Images/saas.png" },
  { name: "AI as a service", image: "/Images/artificial-intelligence.png" },
  { name: "Automated machine learning", image: "/Images/machine-learning.png" },
  { name: "Cross-platform & hybrid development", image: "/Images/hybrid-development.png" },
];

const CustomSlider = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [slidesToShow, setSlidesToShow] = useState(5);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  // Update slidesToShow based on window width
  useEffect(() => {
    const updateSlidesToShow = () => {
      if (window.innerWidth < 768) {
        setSlidesToShow(2);
      } else {
        setSlidesToShow(5);
      }
    };

    updateSlidesToShow();
    window.addEventListener("resize", updateSlidesToShow);
    return () => window.removeEventListener("resize", updateSlidesToShow);
  }, []);

  // Create an array of visible slides using modular arithmetic (for infinite loop effect)
  const getVisibleSlides = () => {
    const visible = [];
    for (let i = 0; i < slidesToShow; i++) {
      visible.push(technologies[(currentIndex + i) % technologies.length]);
    }
    return visible;
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % technologies.length);
  };

  const goToPrev = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + technologies.length) % technologies.length
    );
  };

  return (
    <div className="relative my-24  max-w-5xl mx-auto">
      <h2 className="text-center text-2xl font-bold text-[#7716BC] mb-4">
        Technologies
      </h2>
      <div className="relative">
        {/* Container with horizontal padding to keep arrows inside */}
        <div className="overflow-hidden px-12">
          <div className="flex transition-transform duration-300 ease-in-out">
            {getVisibleSlides().map((tech, index) => (
              <div
                key={index}
                className="w-48 flex-shrink-0 px-2" // Fixed width for uniform boxes
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div
                  className={`flex flex-col items-center justify-center gap-2 p-4 transition-all duration-300 ease-in-out border ${
                    hoveredIndex === index
                      ? "border-purple-400 rounded-lg"
                      : "border-transparent"
                  }`}
                >
                  <span className="bg-purple-100 text-purple-600 rounded-full p-4">
                    <Image
                      src={tech.image}
                      alt={tech.name}
                      width={30}
                      height={30}
                    />
                  </span>
                  <span className="text-center text-sm text-gray-600">
                    {tech.name}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Left Arrow */}
        <button
          onClick={goToPrev}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-2 md:p-3 rounded-full hover:bg-purple-600 z-10"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        {/* Right Arrow */}
        <button
          onClick={goToNext}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-purple-500 text-white p-2 md:p-3 rounded-full hover:bg-purple-600 z-10"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default CustomSlider;
