import React from "react";
import Tab from "@/Components/Portfolio/Tab";
import MobileDev from "@/Components/Portfolio/MobileDev";

const MobileAppPortfolio = () => {
  return (
    <section className="bg-white">
      <div className="max-w-[85vw] mx-auto py-12">
        <div className="w-[70vw] mx-auto">
          <h2 className="text-4xl text-center text-[#F245A1] font-semibold">
            Mobile App Portfolio
          </h2>
          <p className="text-center text-lg font-normal mt-10">
            Discover our collection of innovative mobile applications designed for iOS and Android platforms.
            Our mobile solutions combine intuitive interfaces with powerful functionality to deliver
            exceptional user experiences on the go.
          </p>
        </div>
        <div className="max-w-fit mx-auto my-14">
          <Tab />
        </div>
        
       
      </div>
      <MobileDev/>
    </section>
  );
};

export default MobileAppPortfolio;
