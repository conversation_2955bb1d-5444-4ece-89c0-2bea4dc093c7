import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section3 from "@/Components/Portfolio/WebDev/PaintReady/Section3";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";


const page = () => {
 const images = ["/Images/Akatsuki_mock_1.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/Akatsuki-bg.png"} />
      <Section2
        image={"/Images/Akatsuki1.png"}
        heading={"AKATSUKI-OPVDATA"}
        paragraph1={
          "Akatsuki-opvdata is another FinTech solution we developed for our client. They wanted to have a platform designed specifically for investors where they could get data for trading. This platform collects market data and presents it to investors so they can easily analyze and make data-driven decisions."
        }
        paragraph2={
          "To make this user-friendly, we had to develop features where the data could be presented in the form of charts and graphics. We had to integrate specific features where real-time data could be fetched and presented to the users."
        }
      />
      <Section3
        FrontEndTech={"React, HTML, CSS"}
        BackEndTech={"Node.js, Express.js, MongoDB"}
      />
      <Section4
        paragraph1={
          "Our major challenge was to integrate real-time data into the dashboard and present it in the most convenient way so investors could make lucrative decisions. To make this possible, we used the latest technology stack and leveraged Node.js for backend development and Next.js for frontend development. "
        }
        paragraph2={
          "Akatsuki-opvdata is a unique FinTech app that requires hard work and precision to make it successful. It involved finance-related information that had to be correct, which otherwise would have had unwanted repercussions. However, our expert team successfully delivered the project."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page