import React from "react";
import Image from "next/image";

const ImageCard = ({ imgsrc, altsrc, title, description }) => {
  return (
    <div className="max-w-lg bg-white border border-gray-200 rounded-xl shadow-lg">
      <a href="#">
        {/* Ensure image container is responsive */}
        <div className="relative w-full h-64">
          <Image
            className="rounded-t-lg object-cover"
            src={imgsrc}
            alt={altsrc}
            layout="fill" // This makes the image cover the container
            objectFit="cover" // This ensures the image covers the container while maintaining its aspect ratio
          />
        </div>
      </a>
      <div className="p-5">
        <a href="#">
          <h5 className="mb-2 text-base md:text-lg text-[#7716BC] font-medium tracking-tight">
            {title}
          </h5>
        </a>
        <p className="mb-3 text-sm md:text-base font-normal text-justify">{description}</p>
      </div>
    </div>
  );
};

export default ImageCard;
