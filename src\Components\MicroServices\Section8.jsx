import React from "react";
import HorizontalColoredCard from "../PWA_development/HorizontalColoredCard";

const Section8 = () => {
  const listContent1 = [
    "Examine the high-level business requirements and the factors that influence the deployment of microservices.",
    "Evaluate the viability of microservices.",
    "Make a business case that includes the estimated ROI, TCO, NPV, and microservices implementation.",

    "Create a project strategy for implementing microservices.",
    "Figure out the risks associated with using microservices and create a plan to mitigate them.",
    "Create an architecture for a microservices application. ",
  ];
  const listContent2 = [
    "Examine the high-level business requirements and the factors that influence the deployment of microservices.",
    "Evaluate the viability of microservices.",
    "Make a business case that includes the estimated ROI, TCO, NPV, and microservices implementation.",

    "Create a project strategy for implementing microservices.",
    "Figure out the risks associated with using microservices and create a plan to mitigate them.",
    "Create an architecture for a microservices application. ",
  ];
  const listContent3 = [
    "Examine the needs for both functional and non-functional microservices apps.",
    "Examine microservices' granularity, architecture, communication, and APIs, and suggest areas that require enhancement.",
    "Create a fresh microservices framework.",
    "Determine possible enhancements to the development and deployment procedures, such as automated testing and a continuous deployment pipeline. ",
  ];
  const listContent4 = [
    "Examine the monolithic application architecture in its current state.",
    "feasibility assessment for app upgrading.",
    "(optional) Examine your methods for developing and delivering software, and make suggestions for improvements.",
    "Create fresh architecture based on microservices.",
    "Make a thorough plan for converting the monolith to microservices.",
    "Refactor the historical application or assist the internal team in doing so.",
    "Test and release the updated application.",
  ];
  const listContent5 = [
    "Examine the driving forces behind the deployment of microservices and high-level business requirements.",
    "Examine the readiness for microservices adoption.",
    "(if required) Implement or enhance cloud service utilization, containerization, CI/CD, infrastructure automation, monitoring and management procedures, etc.",
    "Provide a plan for cost minimization for microservices (optional).",
    "Establish KPIs and instruments to gauge the business benefit of implementing microservices.",
    "Applications to switch to the microservices architecture should be chosen and prioritized.",
    "Create microservices.",
    "Create a successful API application. ",
  ];
  const listContent6 = [
    "QA plan, test automation architecture design, and microservices test plan.",
    "setting up the best testing frameworks and tools.",
    "Configuring the test environment and creating test data automatically.",
    "Develop and maintain test automation scripts.",
    "Microservices test artifacts, such as frequent, thorough reporting on defects and test closure, comply with ISO/IEC/IEEE 29119-3:2013.",
  ];
  return (
    <div className="md:w-[95%] mx-auto my-10 md:my-24">
      <h2 className="text-xl md:text-3xl md:leading-[40px] text-center font-semibold">
        <span className="text-[#F245A1]">Case Studies</span> Notable Companies
        That Have Embraced PWAs 
      </h2>
      <div className="items-center gap-3 md:gap-6 my-[32px] mx-[20px] md:mx-[75px]">
        <div className="bg-[#F245A126] flex flex-col gap-3 md:flex-row md:justify-between p-4 md:p-8">
          <HorizontalColoredCard
            heading={"Microservices Advisory"}
            listContent={listContent1}
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
          <HorizontalColoredCard
            heading={"Microservices Development Advisory"}
            listContent={listContent2}
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
        <div className="flex flex-col gap-3 md:flex-row md:justify-between  items-center mt-5 bg-[#794CEC26] p-4 md:p-8">
          <HorizontalColoredCard
            heading={"Microservices Review and Optimization"}
            listContent={listContent3}
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
          <HorizontalColoredCard
            heading={"Rebuilding Legacy Apps into MicroServices"}
            listContent={listContent4}
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
        <div className="bg-[#F245A126] flex flex-col gap-3 md:flex-row md:justify-between mt-5 p-4 md:p-8">
          <HorizontalColoredCard
            heading={"Enterprise-wide Microservices Adoption"}
            listContent={listContent5}
            bgColor={"bg-[#794CEC26] md:w-[40%]"}
          />
          <HorizontalColoredCard
            heading={"Microservices Testing"}
            listContent={listContent6}
            bgColor={"bg-[#F245A126] md:w-[40%]"}
          />
        </div>
      </div>
    </div>
  );
};

export default Section8;
