import React from "react";
import Button from "../Buttons/Button";

const Section5 = () => {
  return (
    <div id="expert-form-section" className="w-[80%] bg-[#ffffff] mx-auto rounded-md mb-10 md:my-24 mt-10">
      <div className="flex p-2">
        <div
          className="md:w-[40%] rounded-md"
          style={{
            backgroundImage: `url(/Images/form-bg.png)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
        <div className="md:w-[60%] p-6">
          <form className="space-y-6">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  placeholder="Name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Company name</label>
                <input
                  type="text"
                  placeholder="company name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  placeholder="Email"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Phone Number</label>
                <input
                  type="phone"
                  placeholder="Phone Number"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Industry Name
                </label>
                <input
                  type="text"
                  placeholder="Industry Name"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Country Region</label>
                <input
                  type="text"
                  placeholder="Country Region"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Job Title
                </label>
                <input
                  type="text"
                  placeholder="Job Title"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">Target Start Date</label>
                <input
                  type="date"
                  placeholder="Target Start Date"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col flex-1">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Total Project budget
                </label>
                <input
                  type="text"
                  placeholder="Project Budget"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
              <div className="flex flex-col flex-1">
                <label className="text-gray-600 mb-1">
                  How did you hear about Valueans
                </label>
                <input
                  type="text"
                  placeholder=""
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-col mt-2">
              <div className="flex flex-col w-full">
                <label htmlFor="" className="text-gray-600 mb-1">
                  Tell us a little about the app you want to build
                </label>
                <input
                  type="text"
                  placeholder="Tell us about your app"
                  className="border-0 border-b-2 border-gray-300 focus:border-b-[#F245A1] focus:outline-none py-2 px-1 transition-colors w-full"
                />
              </div>
            </div>
            <div className="mt-8">
              <Button bgColor="bg-[#F245A1]" paddingX="px-8" paddingY="py-2">
                Submit
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Section5;
