"use client";
import Image from "next/image";
import React, { useState } from "react";

// Mock blog data
const blogs = [
  {
    title: "Flutter App Development Best Practices to Follow in 2023",
    content:
      "The dream of being your boss is a universal aspiration. An online business offers unprecedented freedom and control. You have the liberty to set your own hours, choose your projects, and build a business that aligns with your passions and ...",
    image: "/Images/using_laptops.png",
  },
  {
    title: "React Native Development Tips for 2024",
    content:
      "React Native is a powerful framework for building cross-platform mobile apps. In this blog, we explore some tips to get the most out of your React Native development in 2024 ...",
    image: "/Images/using_laptops.png",
  },
  {
    title: "Top UI/UX Design Trends in 2024",
    content:
      "UI/UX design trends are constantly evolving. In this post, we explore the top trends in 2024, including the rise of minimalism, micro-interactions, and more ...",
    image: "/Images/using_laptops.png",
  },
];

const BlogDisplayCard = () => {
  // State to track the selected blog
  const [selectedBlog, setSelectedBlog] = useState(blogs[0]);

  return (
    
    <div className="max-w-[85%] mx-auto">
      <div className="flex gap-10 mt-12 relative">
        <div className="flex">
          {/* Left div to display the selected blog content */}
          <div
            className="block w-[400px] h-[300px] px-4 py-6 bg-white border-gray-200 rounded-lg shadow hover:bg-gray-100 absolute z-10 left-[-44px] top-[40px] overflow-y-hidden"
            style={{ maxHeight: "300px" }} // Fixed height
          >
            <h5 className="mb-2 text-[32px] font-bold tracking-tight text-black">
              {selectedBlog.title}
            </h5>
            <p className="font-normal text-lg text-black">
              {selectedBlog.content}
            </p>
          </div>

          <div className="w-[648px] h-[440px] relative">
            <Image
              src={selectedBlog.image}
              alt="Selected Blog Image"
              layout="fill"
              objectFit="cover"
            />
          </div>
        </div>

        {/* Right div for the clickable list */}
        <div className="flex-col gap-12">
          {blogs.map((blog, index) => (
            <div
              key={index}
              className="flex gap-8 mt-5 cursor-pointer"
              // onClick={() => setSelectedBlog(blog)}
               // Update state on click
            >
              <div className="w-[115px] h-[108px] relative">
                <Image
                  src={blog.image}
                  alt="Blog Thumbnail"
                  layout="fill"
                  objectFit="cover"
                />
              </div>

              <div className="flex-1">
                <h4 className="text-2xl leading-[36px]">{blog.title}</h4>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BlogDisplayCard;
