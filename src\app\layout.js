import { Poppins, Trirong, Aclonica, Titillium_Web } from "next/font/google";

import "./globals.css";
import Navbar from "@/Components/Header/Navbar";
import Footer from "@/Components/Footer/Footer";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});

const trirong = Trirong({
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
  variable: "--font-trirong",
});

const aclonica = Aclonica({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-aclonica",
});

const titillium = Titillium_Web({
  subsets: ["latin"],
  weight: ["300", "400", "600"],
  variable: "--font-titillium",
});

export const metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${trirong.variable} ${aclonica.variable} ${titillium.variable}`}
    >
      <body className={`${poppins.className} text-gray-900 antialiased`}>
        <Navbar />
        {children}
        <Footer />
      </body>
    </html>
  );
}
