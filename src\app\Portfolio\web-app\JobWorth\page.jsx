import React from 'react'
import Section1 from "@/Components/Portfolio/WebDev/PaintReady/Section1";
import Section2 from "@/Components/Portfolio/WebDev/PaintReady/Section2";
import Section3 from "@/Components/Portfolio/WebDev/PaintReady/Section3";
import Section4 from "@/Components/Portfolio/WebDev/PaintReady/Section4";
import Section5 from "@/Components/Portfolio/WebDev/PaintReady/Section5";


const page = () => {
 const images = ["/Images/JW2.png", "/Images/JW3.png", "/Images/JW4.png"];
  return (
    <div>
      <Section1 backgroundImage={"/Images/JobWorth-bg.png"} />
      <Section2
        image={"/Images/JobWorth1.png"}
        heading={"JobWorth"}
        paragraph1={
          "Our clients wanted a platform where job seekers and employers could connect. They asked us to deliver a solution where both parties can create their profiles and reach out to each other.For this project, they had some requirements such as a search feature to find jobs and employees, a filter feature to set certain criteria to find relevant individuals, and an option to create and personalize a user profile. Moreover, this solution requires a user-friendly interface so that employers and job seekers can easily connect."
        }
        paragraph2={
          "At Valueans, we always prefer using the latest technology stack so that we deliver the most innovative yet efficient solutions for our clients. We were also requested to implement some specific functionalities that can make this process smooth and easy for both employers and job seekers. "
        }
      />
      <Section3
        FrontEndTech={"React, HTML, CSS"}
        BackEndTech={"Node.js, Express.js, MongoDB"}
      />
      <Section4
        paragraph1={
          "The front end was supposed to be extremely easy to use, and for that, we used Vue.js technology. However, for the back end, we chose Django. JobWorth is a successful project that has fulfilled the job-related needs of recruiters and job seekers."
        }
      />
      <Section5 images={images} />
    </div>
  )
}

export default page